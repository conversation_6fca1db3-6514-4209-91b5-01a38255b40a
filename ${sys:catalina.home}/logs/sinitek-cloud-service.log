[ERROR][SIRM][7019,71,http-nio-8097-exec-7][2025-06-23 08:52:31,769][com.sinitek.sirm.framework.support.FrontendResponseSupport.logError:240] - 客户端IP: 127.0.0.1, 当前用户orgId: , 接口/zhida/frontend/api/nocode/form/view发生异常
feign.FeignException$Unauthorized: [401] during [GET] to [http://**************:10001/frontend/api/org/currentuser] [IRemoteOrgUserService#current(String)]: [{"message":"010110","resultcode":"010110","data":null,"success":false}]
	at feign.FeignException.clientErrorStatus(FeignException.java:215) ~[feign-core-11.10.jar:?]
	at feign.FeignException.errorStatus(FeignException.java:194) ~[feign-core-11.10.jar:?]
	at feign.FeignException.errorStatus(FeignException.java:185) ~[feign-core-11.10.jar:?]
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92) ~[feign-core-11.10.jar:?]
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:98) ~[feign-core-11.10.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:141) ~[feign-core-11.10.jar:?]
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:91) ~[feign-core-11.10.jar:?]
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100) ~[feign-core-11.10.jar:?]
	at org.springframework.cloud.openfeign.FeignCachingInvocationHandlerFactory$1.proceed(FeignCachingInvocationHandlerFactory.java:66) ~[spring-cloud-openfeign-core-3.1.9.jar:3.1.9]
	at org.springframework.cache.interceptor.CacheInterceptor.lambda$invoke$0(CacheInterceptor.java:54) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:351) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.cache.interceptor.CacheInterceptor.invoke(CacheInterceptor.java:64) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.cloud.openfeign.FeignCachingInvocationHandlerFactory.lambda$create$1(FeignCachingInvocationHandlerFactory.java:53) ~[spring-cloud-openfeign-core-3.1.9.jar:3.1.9]
	at com.sun.proxy.$Proxy271.current(Unknown Source) ~[?:?]
	at sun.reflect.GeneratedMethodAccessor100.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sun.proxy.$Proxy147.current(Unknown Source) ~[?:?]
	at sun.reflect.GeneratedMethodAccessor100.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sun.proxy.$Proxy147.current(Unknown Source) ~[?:?]
	at com.sinitek.sirm.nocode.common.config.TokenCreateInterceptor.preHandle(TokenCreateInterceptor.java:86) ~[classes/:?]
	at org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:148) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.github.xiaoymin.knife4j.spring.filter.SecurityBasicAuthFilter.doFilter(SecurityBasicAuthFilter.java:87) ~[knife4j-spring-2.0.8.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.11.jar:1.2.11]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
[ERROR][SIRM][7019,72,http-nio-8097-exec-8][2025-06-23 08:52:31,768][com.sinitek.sirm.framework.support.FrontendResponseSupport.logError:240] - 客户端IP: 127.0.0.1, 当前用户orgId: , 接口/zhida/frontend/api/nocode/page/group-list发生异常
feign.FeignException$Unauthorized: [401] during [GET] to [http://**************:10001/frontend/api/org/currentuser] [IRemoteOrgUserService#current(String)]: [{"message":"010110","resultcode":"010110","data":null,"success":false}]
	at feign.FeignException.clientErrorStatus(FeignException.java:215) ~[feign-core-11.10.jar:?]
	at feign.FeignException.errorStatus(FeignException.java:194) ~[feign-core-11.10.jar:?]
	at feign.FeignException.errorStatus(FeignException.java:185) ~[feign-core-11.10.jar:?]
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92) ~[feign-core-11.10.jar:?]
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:98) ~[feign-core-11.10.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:141) ~[feign-core-11.10.jar:?]
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:91) ~[feign-core-11.10.jar:?]
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100) ~[feign-core-11.10.jar:?]
	at org.springframework.cloud.openfeign.FeignCachingInvocationHandlerFactory$1.proceed(FeignCachingInvocationHandlerFactory.java:66) ~[spring-cloud-openfeign-core-3.1.9.jar:3.1.9]
	at org.springframework.cache.interceptor.CacheInterceptor.lambda$invoke$0(CacheInterceptor.java:54) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:351) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.cache.interceptor.CacheInterceptor.invoke(CacheInterceptor.java:64) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.cloud.openfeign.FeignCachingInvocationHandlerFactory.lambda$create$1(FeignCachingInvocationHandlerFactory.java:53) ~[spring-cloud-openfeign-core-3.1.9.jar:3.1.9]
	at com.sun.proxy.$Proxy271.current(Unknown Source) ~[?:?]
	at sun.reflect.GeneratedMethodAccessor100.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sun.proxy.$Proxy147.current(Unknown Source) ~[?:?]
	at sun.reflect.GeneratedMethodAccessor100.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sun.proxy.$Proxy147.current(Unknown Source) ~[?:?]
	at com.sinitek.sirm.nocode.common.config.TokenCreateInterceptor.preHandle(TokenCreateInterceptor.java:86) ~[classes/:?]
	at org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:148) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.github.xiaoymin.knife4j.spring.filter.SecurityBasicAuthFilter.doFilter(SecurityBasicAuthFilter.java:87) ~[knife4j-spring-2.0.8.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.11.jar:1.2.11]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
[ERROR][SIRM][7019,73,http-nio-8097-exec-9][2025-06-23 08:52:31,769][com.sinitek.sirm.framework.support.FrontendResponseSupport.logError:240] - 客户端IP: 127.0.0.1, 当前用户orgId: , 接口/zhida/frontend/api/nocode/app/setting发生异常
feign.FeignException$Unauthorized: [401] during [GET] to [http://**************:10001/frontend/api/org/currentuser] [IRemoteOrgUserService#current(String)]: [{"message":"010110","resultcode":"010110","data":null,"success":false}]
	at feign.FeignException.clientErrorStatus(FeignException.java:215) ~[feign-core-11.10.jar:?]
	at feign.FeignException.errorStatus(FeignException.java:194) ~[feign-core-11.10.jar:?]
	at feign.FeignException.errorStatus(FeignException.java:185) ~[feign-core-11.10.jar:?]
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92) ~[feign-core-11.10.jar:?]
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:98) ~[feign-core-11.10.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:141) ~[feign-core-11.10.jar:?]
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:91) ~[feign-core-11.10.jar:?]
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100) ~[feign-core-11.10.jar:?]
	at org.springframework.cloud.openfeign.FeignCachingInvocationHandlerFactory$1.proceed(FeignCachingInvocationHandlerFactory.java:66) ~[spring-cloud-openfeign-core-3.1.9.jar:3.1.9]
	at org.springframework.cache.interceptor.CacheInterceptor.lambda$invoke$0(CacheInterceptor.java:54) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:351) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.cache.interceptor.CacheInterceptor.invoke(CacheInterceptor.java:64) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.cloud.openfeign.FeignCachingInvocationHandlerFactory.lambda$create$1(FeignCachingInvocationHandlerFactory.java:53) ~[spring-cloud-openfeign-core-3.1.9.jar:3.1.9]
	at com.sun.proxy.$Proxy271.current(Unknown Source) ~[?:?]
	at sun.reflect.GeneratedMethodAccessor100.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sun.proxy.$Proxy147.current(Unknown Source) ~[?:?]
	at sun.reflect.GeneratedMethodAccessor100.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sun.proxy.$Proxy147.current(Unknown Source) ~[?:?]
	at com.sinitek.sirm.nocode.common.config.TokenCreateInterceptor.preHandle(TokenCreateInterceptor.java:86) ~[classes/:?]
	at org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:148) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.github.xiaoymin.knife4j.spring.filter.SecurityBasicAuthFilter.doFilter(SecurityBasicAuthFilter.java:87) ~[knife4j-spring-2.0.8.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.11.jar:1.2.11]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
[ERROR][SIRM][7019,67,http-nio-8097-exec-3][2025-06-23 08:52:31,769][com.sinitek.sirm.framework.support.FrontendResponseSupport.logError:240] - 客户端IP: 127.0.0.1, 当前用户orgId: , 接口/zhida/frontend/api/nocode/page/group-tree发生异常
feign.FeignException$Unauthorized: [401] during [GET] to [http://**************:10001/frontend/api/org/currentuser] [IRemoteOrgUserService#current(String)]: [{"message":"010110","resultcode":"010110","data":null,"success":false}]
	at feign.FeignException.clientErrorStatus(FeignException.java:215) ~[feign-core-11.10.jar:?]
	at feign.FeignException.errorStatus(FeignException.java:194) ~[feign-core-11.10.jar:?]
	at feign.FeignException.errorStatus(FeignException.java:185) ~[feign-core-11.10.jar:?]
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92) ~[feign-core-11.10.jar:?]
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:98) ~[feign-core-11.10.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:141) ~[feign-core-11.10.jar:?]
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:91) ~[feign-core-11.10.jar:?]
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100) ~[feign-core-11.10.jar:?]
	at org.springframework.cloud.openfeign.FeignCachingInvocationHandlerFactory$1.proceed(FeignCachingInvocationHandlerFactory.java:66) ~[spring-cloud-openfeign-core-3.1.9.jar:3.1.9]
	at org.springframework.cache.interceptor.CacheInterceptor.lambda$invoke$0(CacheInterceptor.java:54) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:351) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.cache.interceptor.CacheInterceptor.invoke(CacheInterceptor.java:64) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.cloud.openfeign.FeignCachingInvocationHandlerFactory.lambda$create$1(FeignCachingInvocationHandlerFactory.java:53) ~[spring-cloud-openfeign-core-3.1.9.jar:3.1.9]
	at com.sun.proxy.$Proxy271.current(Unknown Source) ~[?:?]
	at sun.reflect.GeneratedMethodAccessor100.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sun.proxy.$Proxy147.current(Unknown Source) ~[?:?]
	at sun.reflect.GeneratedMethodAccessor100.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sun.proxy.$Proxy147.current(Unknown Source) ~[?:?]
	at com.sinitek.sirm.nocode.common.config.TokenCreateInterceptor.preHandle(TokenCreateInterceptor.java:86) ~[classes/:?]
	at org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:148) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.github.xiaoymin.knife4j.spring.filter.SecurityBasicAuthFilter.doFilter(SecurityBasicAuthFilter.java:87) ~[knife4j-spring-2.0.8.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.11.jar:1.2.11]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
[ERROR][SIRM][7019,70,http-nio-8097-exec-6][2025-06-23 08:52:31,768][com.sinitek.sirm.framework.support.FrontendResponseSupport.logError:240] - 客户端IP: 127.0.0.1, 当前用户orgId: , 接口/zhida/frontend/api/nocode/page/list发生异常
feign.FeignException$Unauthorized: [401] during [GET] to [http://**************:10001/frontend/api/org/currentuser] [IRemoteOrgUserService#current(String)]: [{"message":"010110","resultcode":"010110","data":null,"success":false}]
	at feign.FeignException.clientErrorStatus(FeignException.java:215) ~[feign-core-11.10.jar:?]
	at feign.FeignException.errorStatus(FeignException.java:194) ~[feign-core-11.10.jar:?]
	at feign.FeignException.errorStatus(FeignException.java:185) ~[feign-core-11.10.jar:?]
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92) ~[feign-core-11.10.jar:?]
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:98) ~[feign-core-11.10.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:141) ~[feign-core-11.10.jar:?]
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:91) ~[feign-core-11.10.jar:?]
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100) ~[feign-core-11.10.jar:?]
	at org.springframework.cloud.openfeign.FeignCachingInvocationHandlerFactory$1.proceed(FeignCachingInvocationHandlerFactory.java:66) ~[spring-cloud-openfeign-core-3.1.9.jar:3.1.9]
	at org.springframework.cache.interceptor.CacheInterceptor.lambda$invoke$0(CacheInterceptor.java:54) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:351) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.cache.interceptor.CacheInterceptor.invoke(CacheInterceptor.java:64) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.cloud.openfeign.FeignCachingInvocationHandlerFactory.lambda$create$1(FeignCachingInvocationHandlerFactory.java:53) ~[spring-cloud-openfeign-core-3.1.9.jar:3.1.9]
	at com.sun.proxy.$Proxy271.current(Unknown Source) ~[?:?]
	at sun.reflect.GeneratedMethodAccessor100.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sun.proxy.$Proxy147.current(Unknown Source) ~[?:?]
	at sun.reflect.GeneratedMethodAccessor100.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sun.proxy.$Proxy147.current(Unknown Source) ~[?:?]
	at com.sinitek.sirm.nocode.common.config.TokenCreateInterceptor.preHandle(TokenCreateInterceptor.java:86) ~[classes/:?]
	at org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:148) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.github.xiaoymin.knife4j.spring.filter.SecurityBasicAuthFilter.doFilter(SecurityBasicAuthFilter.java:87) ~[knife4j-spring-2.0.8.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.11.jar:1.2.11]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
[ERROR][SIRM][7019,66,http-nio-8097-exec-2][2025-06-23 08:52:31,768][com.sinitek.sirm.framework.support.FrontendResponseSupport.logError:240] - 客户端IP: 127.0.0.1, 当前用户orgId: , 接口/zhida/frontend/api/nocode/page/get-name发生异常
feign.FeignException$Unauthorized: [401] during [GET] to [http://**************:10001/frontend/api/org/currentuser] [IRemoteOrgUserService#current(String)]: [{"message":"010110","resultcode":"010110","data":null,"success":false}]
	at feign.FeignException.clientErrorStatus(FeignException.java:215) ~[feign-core-11.10.jar:?]
	at feign.FeignException.errorStatus(FeignException.java:194) ~[feign-core-11.10.jar:?]
	at feign.FeignException.errorStatus(FeignException.java:185) ~[feign-core-11.10.jar:?]
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92) ~[feign-core-11.10.jar:?]
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:98) ~[feign-core-11.10.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:141) ~[feign-core-11.10.jar:?]
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:91) ~[feign-core-11.10.jar:?]
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100) ~[feign-core-11.10.jar:?]
	at org.springframework.cloud.openfeign.FeignCachingInvocationHandlerFactory$1.proceed(FeignCachingInvocationHandlerFactory.java:66) ~[spring-cloud-openfeign-core-3.1.9.jar:3.1.9]
	at org.springframework.cache.interceptor.CacheInterceptor.lambda$invoke$0(CacheInterceptor.java:54) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:351) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.cache.interceptor.CacheInterceptor.invoke(CacheInterceptor.java:64) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.cloud.openfeign.FeignCachingInvocationHandlerFactory.lambda$create$1(FeignCachingInvocationHandlerFactory.java:53) ~[spring-cloud-openfeign-core-3.1.9.jar:3.1.9]
	at com.sun.proxy.$Proxy271.current(Unknown Source) ~[?:?]
	at sun.reflect.GeneratedMethodAccessor100.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sun.proxy.$Proxy147.current(Unknown Source) ~[?:?]
	at sun.reflect.GeneratedMethodAccessor100.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sun.proxy.$Proxy147.current(Unknown Source) ~[?:?]
	at com.sinitek.sirm.nocode.common.config.TokenCreateInterceptor.preHandle(TokenCreateInterceptor.java:86) ~[classes/:?]
	at org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:148) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.github.xiaoymin.knife4j.spring.filter.SecurityBasicAuthFilter.doFilter(SecurityBasicAuthFilter.java:87) ~[knife4j-spring-2.0.8.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.11.jar:1.2.11]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
[ERROR][SIRM][7019,65,http-nio-8097-exec-1][2025-06-23 08:52:33,597][com.sinitek.sirm.framework.support.FrontendResponseSupport.logError:240] - 客户端IP: 127.0.0.1, 当前用户orgId: , 接口/zhida/frontend/api/nocode/form/view发生异常
feign.FeignException$Unauthorized: [401] during [GET] to [http://**************:10001/frontend/api/org/currentuser] [IRemoteOrgUserService#current(String)]: [{"message":"010110","resultcode":"010110","data":null,"success":false}]
	at feign.FeignException.clientErrorStatus(FeignException.java:215) ~[feign-core-11.10.jar:?]
	at feign.FeignException.errorStatus(FeignException.java:194) ~[feign-core-11.10.jar:?]
	at feign.FeignException.errorStatus(FeignException.java:185) ~[feign-core-11.10.jar:?]
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92) ~[feign-core-11.10.jar:?]
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:98) ~[feign-core-11.10.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:141) ~[feign-core-11.10.jar:?]
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:91) ~[feign-core-11.10.jar:?]
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100) ~[feign-core-11.10.jar:?]
	at org.springframework.cloud.openfeign.FeignCachingInvocationHandlerFactory$1.proceed(FeignCachingInvocationHandlerFactory.java:66) ~[spring-cloud-openfeign-core-3.1.9.jar:3.1.9]
	at org.springframework.cache.interceptor.CacheInterceptor.lambda$invoke$0(CacheInterceptor.java:54) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:351) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.cache.interceptor.CacheInterceptor.invoke(CacheInterceptor.java:64) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.cloud.openfeign.FeignCachingInvocationHandlerFactory.lambda$create$1(FeignCachingInvocationHandlerFactory.java:53) ~[spring-cloud-openfeign-core-3.1.9.jar:3.1.9]
	at com.sun.proxy.$Proxy271.current(Unknown Source) ~[?:?]
	at sun.reflect.GeneratedMethodAccessor100.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sun.proxy.$Proxy147.current(Unknown Source) ~[?:?]
	at sun.reflect.GeneratedMethodAccessor100.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sun.proxy.$Proxy147.current(Unknown Source) ~[?:?]
	at com.sinitek.sirm.nocode.common.config.TokenCreateInterceptor.preHandle(TokenCreateInterceptor.java:86) ~[classes/:?]
	at org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:148) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.github.xiaoymin.knife4j.spring.filter.SecurityBasicAuthFilter.doFilter(SecurityBasicAuthFilter.java:87) ~[knife4j-spring-2.0.8.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.11.jar:1.2.11]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
[INFO][SIRM][7019,71,http-nio-8097-exec-7][2025-06-23 08:58:09,440][com.sinitek.sirm.nocode.controller.ZdLlmController.agentGenerateDiagram:132] - 开始生成图表，表单代码: page_36f15be5f7af4f43a8229d8703e05133, 需求: 统计考试成绩，语文和数学的平均分。用柱状图表示
[INFO][SIRM][7019,71,http-nio-8097-exec-7][2025-06-23 08:58:09,445][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.formSqlGenerate:343] - 开始生成SQL，表单代码: page_36f15be5f7af4f43a8229d8703e05133, 需求: 统计考试成绩，语文和数学的平均分。用柱状图表示
[INFO][SIRM][7019,71,http-nio-8097-exec-7][2025-06-23 08:58:46,972][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.formSqlGenerate:353] - SQL生成完成，表单代码: page_36f15be5f7af4f43a8229d8703e05133, SQL长度: 583
[INFO][SIRM][7019,71,http-nio-8097-exec-7][2025-06-23 08:58:46,977][com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery:84] - 执行动态SQL查询: SELECT 
    subject_data ->> 'ZDInput_jvy2' AS subject,
    AVG((score_data ->> 'ZDNumber_wx80')::numeric) AS avg_score
FROM 
    zd_page_form_data_64,
    jsonb_array_elements(form_data -> 'model' -> 'ZDChildForm_zolh') AS child_form,
    jsonb_array_elements(child_form -> 'children') AS subject_record,
    jsonb_array_elements(subject_record -> 'model') AS subject_data,
    jsonb_array_elements(subject_record -> 'model') AS score_data
WHERE 
    subject_data ->> 'ZDInput_jvy2' IN ('语文', '数学')
    AND score_data ? 'ZDNumber_wx80'
GROUP BY 
    subject_data ->> 'ZDInput_jvy2';
[ERROR][SIRM][7019,71,http-nio-8097-exec-7][2025-06-23 08:58:47,033][com.alibaba.druid.filter.logging.Log4jFilter.statementLogError:151] - {conn-10018, pstmt-20159} execute error. SELECT 
    subject_data ->> 'ZDInput_jvy2' AS subject,
    AVG((score_data ->> 'ZDNumber_wx80')::numeric) AS avg_score
FROM 
    zd_page_form_data_64,
    jsonb_array_elements(form_data -> 'model' -> 'ZDChildForm_zolh') AS child_form,
    jsonb_array_elements(child_form -> 'children') AS subject_record,
    jsonb_array_elements(subject_record -> 'model') AS subject_data,
    jsonb_array_elements(subject_record -> 'model') AS score_data
WHERE 
    subject_data ->> 'ZDInput_jvy2' IN ('语文', '数学')
    AND score_data ? 'ZDNumber_wx80'
GROUP BY 
    subject_data ->> 'ZDInput_jvy2';
org.postgresql.util.PSQLException: 未设定参数值 1 的内容。
	at org.postgresql.core.v3.SimpleParameterList.checkAllParametersSet(SimpleParameterList.java:284) ~[postgresql-42.3.1.jar:42.3.1]
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:338) ~[postgresql-42.3.1.jar:42.3.1]
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:484) ~[postgresql-42.3.1.jar:42.3.1]
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:404) ~[postgresql-42.3.1.jar:42.3.1]
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:162) ~[postgresql-42.3.1.jar:42.3.1]
	at org.postgresql.jdbc.PgPreparedStatement.execute(PgPreparedStatement.java:151) ~[postgresql-42.3.1.jar:42.3.1]
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3446) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.wall.WallFilter.preparedStatement_execute(WallFilter.java:638) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:152) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483) ~[druid-1.2.11.jar:1.2.11]
	at sun.reflect.GeneratedMethodAccessor108.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy275.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79) ~[mybatis-3.5.6.jar:3.5.6]
	at sun.reflect.GeneratedMethodAccessor98.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy274.query(Unknown Source) ~[?:?]
	at sun.reflect.GeneratedMethodAccessor98.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy274.query(Unknown Source) ~[?:?]
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doQuery(MybatisSimpleExecutor.java:69) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:165) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:92) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at sun.reflect.GeneratedMethodAccessor107.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy273.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.6.jar:3.5.6]
	at sun.reflect.GeneratedMethodAccessor168.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at com.sun.proxy.$Proxy140.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:223) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:173) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:78) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.sun.proxy.$Proxy141.executeDynamicQuery(Unknown Source) ~[?:?]
	at com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery(SqlQueryUtil.java:91) ~[classes/:?]
	at com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery(SqlQueryUtil.java:63) ~[classes/:?]
	at com.sinitek.sirm.nocode.controller.ZdLlmController.agentGenerateDiagram(ZdLlmController.java:141) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.github.xiaoymin.knife4j.spring.filter.SecurityBasicAuthFilter.doFilter(SecurityBasicAuthFilter.java:87) ~[knife4j-spring-2.0.8.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.11.jar:1.2.11]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
[ERROR][SIRM][7019,71,http-nio-8097-exec-7][2025-06-23 08:58:47,047][com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery:106] - 执行SQL查询失败: SELECT 
    subject_data ->> 'ZDInput_jvy2' AS subject,
    AVG((score_data ->> 'ZDNumber_wx80')::numeric) AS avg_score
FROM 
    zd_page_form_data_64,
    jsonb_array_elements(form_data -> 'model' -> 'ZDChildForm_zolh') AS child_form,
    jsonb_array_elements(child_form -> 'children') AS subject_record,
    jsonb_array_elements(subject_record -> 'model') AS subject_data,
    jsonb_array_elements(subject_record -> 'model') AS score_data
WHERE 
    subject_data ->> 'ZDInput_jvy2' IN ('语文', '数学')
    AND score_data ? 'ZDNumber_wx80'
GROUP BY 
    subject_data ->> 'ZDInput_jvy2';
org.springframework.dao.DataIntegrityViolationException: 
### Error querying database.  Cause: org.postgresql.util.PSQLException: 未设定参数值 1 的内容。
### The error may exist in com/sinitek/sirm/nocode/common/mapper/CommonMapper.xml
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT      subject_data ->> 'ZDInput_jvy2' AS subject,     AVG((score_data ->> 'ZDNumber_wx80')::numeric) AS avg_score FROM      zd_page_form_data_64,     jsonb_array_elements(form_data -> 'model' -> 'ZDChildForm_zolh') AS child_form,     jsonb_array_elements(child_form -> 'children') AS subject_record,     jsonb_array_elements(subject_record -> 'model') AS subject_data,     jsonb_array_elements(subject_record -> 'model') AS score_data WHERE      subject_data ->> 'ZDInput_jvy2' IN ('语文', '数学')     AND score_data ? 'ZDNumber_wx80' GROUP BY      subject_data ->> 'ZDInput_jvy2';
### Cause: org.postgresql.util.PSQLException: 未设定参数值 1 的内容。
; 未设定参数值 1 的内容。; nested exception is org.postgresql.util.PSQLException: 未设定参数值 1 的内容。
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104) ~[spring-jdbc-5.3.39.jar:5.3.39]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73) ~[spring-jdbc-5.3.39.jar:5.3.39]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.39.jar:5.3.39]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.39.jar:5.3.39]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:88) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at com.sun.proxy.$Proxy140.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:223) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:173) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:78) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.sun.proxy.$Proxy141.executeDynamicQuery(Unknown Source) ~[?:?]
	at com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery(SqlQueryUtil.java:91) ~[classes/:?]
	at com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery(SqlQueryUtil.java:63) ~[classes/:?]
	at com.sinitek.sirm.nocode.controller.ZdLlmController.agentGenerateDiagram(ZdLlmController.java:141) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.github.xiaoymin.knife4j.spring.filter.SecurityBasicAuthFilter.doFilter(SecurityBasicAuthFilter.java:87) ~[knife4j-spring-2.0.8.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.11.jar:1.2.11]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: org.postgresql.util.PSQLException: 未设定参数值 1 的内容。
	at org.postgresql.core.v3.SimpleParameterList.checkAllParametersSet(SimpleParameterList.java:284) ~[postgresql-42.3.1.jar:42.3.1]
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:338) ~[postgresql-42.3.1.jar:42.3.1]
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:484) ~[postgresql-42.3.1.jar:42.3.1]
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:404) ~[postgresql-42.3.1.jar:42.3.1]
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:162) ~[postgresql-42.3.1.jar:42.3.1]
	at org.postgresql.jdbc.PgPreparedStatement.execute(PgPreparedStatement.java:151) ~[postgresql-42.3.1.jar:42.3.1]
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3446) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.wall.WallFilter.preparedStatement_execute(WallFilter.java:638) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:152) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483) ~[druid-1.2.11.jar:1.2.11]
	at sun.reflect.GeneratedMethodAccessor108.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy275.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79) ~[mybatis-3.5.6.jar:3.5.6]
	at sun.reflect.GeneratedMethodAccessor98.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy274.query(Unknown Source) ~[?:?]
	at sun.reflect.GeneratedMethodAccessor98.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy274.query(Unknown Source) ~[?:?]
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doQuery(MybatisSimpleExecutor.java:69) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:165) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:92) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at sun.reflect.GeneratedMethodAccessor107.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy273.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.6.jar:3.5.6]
	at sun.reflect.GeneratedMethodAccessor168.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426) ~[mybatis-spring-2.0.5.jar:2.0.5]
	... 66 more
[ERROR][SIRM][7019,71,http-nio-8097-exec-7][2025-06-23 08:58:47,059][com.sinitek.sirm.nocode.controller.ZdLlmController.agentGenerateDiagram:154] - 图表生成失败，表单代码: page_36f15be5f7af4f43a8229d8703e05133, 错误: SQL查询执行失败: 
### Error querying database.  Cause: org.postgresql.util.PSQLException: 未设定参数值 1 的内容。
### The error may exist in com/sinitek/sirm/nocode/common/mapper/CommonMapper.xml
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT      subject_data ->> 'ZDInput_jvy2' AS subject,     AVG((score_data ->> 'ZDNumber_wx80')::numeric) AS avg_score FROM      zd_page_form_data_64,     jsonb_array_elements(form_data -> 'model' -> 'ZDChildForm_zolh') AS child_form,     jsonb_array_elements(child_form -> 'children') AS subject_record,     jsonb_array_elements(subject_record -> 'model') AS subject_data,     jsonb_array_elements(subject_record -> 'model') AS score_data WHERE      subject_data ->> 'ZDInput_jvy2' IN ('语文', '数学')     AND score_data ? 'ZDNumber_wx80' GROUP BY      subject_data ->> 'ZDInput_jvy2';
### Cause: org.postgresql.util.PSQLException: 未设定参数值 1 的内容。
; 未设定参数值 1 的内容。; nested exception is org.postgresql.util.PSQLException: 未设定参数值 1 的内容。
com.sinitek.sirm.framework.exception.BussinessException: SQL查询执行失败: 
### Error querying database.  Cause: org.postgresql.util.PSQLException: 未设定参数值 1 的内容。
### The error may exist in com/sinitek/sirm/nocode/common/mapper/CommonMapper.xml
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT      subject_data ->> 'ZDInput_jvy2' AS subject,     AVG((score_data ->> 'ZDNumber_wx80')::numeric) AS avg_score FROM      zd_page_form_data_64,     jsonb_array_elements(form_data -> 'model' -> 'ZDChildForm_zolh') AS child_form,     jsonb_array_elements(child_form -> 'children') AS subject_record,     jsonb_array_elements(subject_record -> 'model') AS subject_data,     jsonb_array_elements(subject_record -> 'model') AS score_data WHERE      subject_data ->> 'ZDInput_jvy2' IN ('语文', '数学')     AND score_data ? 'ZDNumber_wx80' GROUP BY      subject_data ->> 'ZDInput_jvy2';
### Cause: org.postgresql.util.PSQLException: 未设定参数值 1 的内容。
; 未设定参数值 1 的内容。; nested exception is org.postgresql.util.PSQLException: 未设定参数值 1 的内容。
	at com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery(SqlQueryUtil.java:110) ~[classes/:?]
	at com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery(SqlQueryUtil.java:63) ~[classes/:?]
	at com.sinitek.sirm.nocode.controller.ZdLlmController.agentGenerateDiagram(ZdLlmController.java:141) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.github.xiaoymin.knife4j.spring.filter.SecurityBasicAuthFilter.doFilter(SecurityBasicAuthFilter.java:87) ~[knife4j-spring-2.0.8.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.11.jar:1.2.11]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
[ERROR][SIRM][7019,71,http-nio-8097-exec-7][2025-06-23 08:58:47,122][com.sinitek.sirm.framework.support.FrontendResponseSupport.logError:240] - 客户端IP: 127.0.0.1, 当前用户orgId: *********, 接口/zhida/frontend/api/nocode/llm/agent-generate-diagram发生异常
java.lang.RuntimeException: 图表生成失败: SQL查询执行失败: 
### Error querying database.  Cause: org.postgresql.util.PSQLException: 未设定参数值 1 的内容。
### The error may exist in com/sinitek/sirm/nocode/common/mapper/CommonMapper.xml
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT      subject_data ->> 'ZDInput_jvy2' AS subject,     AVG((score_data ->> 'ZDNumber_wx80')::numeric) AS avg_score FROM      zd_page_form_data_64,     jsonb_array_elements(form_data -> 'model' -> 'ZDChildForm_zolh') AS child_form,     jsonb_array_elements(child_form -> 'children') AS subject_record,     jsonb_array_elements(subject_record -> 'model') AS subject_data,     jsonb_array_elements(subject_record -> 'model') AS score_data WHERE      subject_data ->> 'ZDInput_jvy2' IN ('语文', '数学')     AND score_data ? 'ZDNumber_wx80' GROUP BY      subject_data ->> 'ZDInput_jvy2';
### Cause: org.postgresql.util.PSQLException: 未设定参数值 1 的内容。
; 未设定参数值 1 的内容。; nested exception is org.postgresql.util.PSQLException: 未设定参数值 1 的内容。
	at com.sinitek.sirm.nocode.controller.ZdLlmController.agentGenerateDiagram(ZdLlmController.java:155) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.github.xiaoymin.knife4j.spring.filter.SecurityBasicAuthFilter.doFilter(SecurityBasicAuthFilter.java:87) ~[knife4j-spring-2.0.8.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.11.jar:1.2.11]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: com.sinitek.sirm.framework.exception.BussinessException: SQL查询执行失败: 
### Error querying database.  Cause: org.postgresql.util.PSQLException: 未设定参数值 1 的内容。
### The error may exist in com/sinitek/sirm/nocode/common/mapper/CommonMapper.xml
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT      subject_data ->> 'ZDInput_jvy2' AS subject,     AVG((score_data ->> 'ZDNumber_wx80')::numeric) AS avg_score FROM      zd_page_form_data_64,     jsonb_array_elements(form_data -> 'model' -> 'ZDChildForm_zolh') AS child_form,     jsonb_array_elements(child_form -> 'children') AS subject_record,     jsonb_array_elements(subject_record -> 'model') AS subject_data,     jsonb_array_elements(subject_record -> 'model') AS score_data WHERE      subject_data ->> 'ZDInput_jvy2' IN ('语文', '数学')     AND score_data ? 'ZDNumber_wx80' GROUP BY      subject_data ->> 'ZDInput_jvy2';
### Cause: org.postgresql.util.PSQLException: 未设定参数值 1 的内容。
; 未设定参数值 1 的内容。; nested exception is org.postgresql.util.PSQLException: 未设定参数值 1 的内容。
	at com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery(SqlQueryUtil.java:110) ~[classes/:?]
	at com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery(SqlQueryUtil.java:63) ~[classes/:?]
	at com.sinitek.sirm.nocode.controller.ZdLlmController.agentGenerateDiagram(ZdLlmController.java:141) ~[classes/:?]
	... 56 more
[WARN][SIRM][7019,28,Thread-8][2025-06-23 09:06:14,707][com.alibaba.nacos.common.notify.NotifyCenter.shutdown:136] - [NotifyCenter] Start destroying Publisher
[WARN][SIRM][7019,19,Thread-2][2025-06-23 09:06:14,707][com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown:102] - [HttpClientBeanHolder] Start destroying common HttpClient
[WARN][SIRM][7019,28,Thread-8][2025-06-23 09:06:14,722][com.alibaba.nacos.common.notify.NotifyCenter.shutdown:153] - [NotifyCenter] Destruction of the end
[WARN][SIRM][7019,19,Thread-2][2025-06-23 09:06:14,732][com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown:111] - [HttpClientBeanHolder] Destruction of the end
[INFO][SIRM][7019,46,SpringApplicationShutdownHook][2025-06-23 09:06:15,826][com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister:95] - De-registering from Nacos Server now...
[INFO][SIRM][7019,46,SpringApplicationShutdownHook][2025-06-23 09:06:15,897][com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister:115] - De-registration finished.
[INFO][SIRM][7019,46,SpringApplicationShutdownHook][2025-06-23 09:06:15,922][com.sinitek.sirm.nocode.support.autoconfigure.FlywayAutoConfiguration.close:56] - 关闭flyway数据源
[INFO][SIRM][7019,46,SpringApplicationShutdownHook][2025-06-23 09:06:15,972][com.alibaba.druid.pool.DruidDataSource.close:2051] - {dataSource-1} closing ...
[INFO][SIRM][7019,46,SpringApplicationShutdownHook][2025-06-23 09:06:15,979][com.alibaba.druid.pool.DruidDataSource.close:2124] - {dataSource-1} closed
[INFO][SIRM][61896,1,main][2025-06-23 09:09:47,283][com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[INFO][SIRM][61896,18,background-preinit][2025-06-23 09:09:47,306][org.hibernate.validator.internal.util.Version.<clinit>:21] - HV000001: Hibernate Validator 6.2.5.Final
[INFO][SIRM][61896,1,main][2025-06-23 09:09:48,173][com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO][SIRM][61896,1,main][2025-06-23 09:09:48,174][com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[WARN][SIRM][61896,1,main][2025-06-23 09:09:49,678][com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData:97] - Ignore the empty nacos configuration and get it based on dataId[sinitek-nocode-backend] & group[DEFAULT_GROUP]
[WARN][SIRM][61896,1,main][2025-06-23 09:09:49,776][com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData:97] - Ignore the empty nacos configuration and get it based on dataId[sinitek-nocode-backend-local.yml] & group[DEFAULT_GROUP]
[INFO][SIRM][61896,1,main][2025-06-23 09:09:49,778][org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.doInitialize:134] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-sinitek-nocode-backend-local.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-sinitek-nocode-backend.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-sinitek-nocode-backend,DEFAULT_GROUP'}]
[INFO][SIRM][61896,1,main][2025-06-23 09:09:49,928][org.springframework.boot.SpringApplication.logStartupProfileInfo:651] - The following 1 profile is active: "local"
[INFO][SIRM][61896,1,main][2025-06-23 09:09:52,916][org.springframework.cloud.context.scope.GenericScope.setSerializationId:283] - BeanFactory id=bcd20513-c4ed-332b-9709-cdff1cb9c6d2
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,115][com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory:39] - Post-processing PropertySource instances
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,118][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource bootstrapProperties-sinitek-nocode-backend-local.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,118][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource bootstrapProperties-sinitek-nocode-backend.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,119][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource bootstrapProperties-sinitek-nocode-backend,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,160][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,161][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,162][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,162][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,162][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,163][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,163][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,163][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,163][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,163][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource Config resource 'class path resource [bootstrap-local.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,163][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,163][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,187][com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2:31] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,277][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'hibernateValidateConfig' of type [com.sinitek.sirm.config.HibernateValidateConfig$$EnhancerBySpringCGLIB$$192c7aaa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,281][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'springMessageConfig' of type [com.sinitek.sirm.config.SpringMessageConfig$$EnhancerBySpringCGLIB$$1dd2a9c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,293][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,298][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,299][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$494/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,303][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,307][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,308][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'stringOrNumberMigrationVersionConverter' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration$StringOrNumberToMigrationVersionConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,342][com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2:35] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,346][com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2:35] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,397][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'i18nProperties' of type [com.sinitek.sirm.i18n.properties.I18nProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,418][com.sinitek.sirm.config.SpringMessageConfig.messageSource:52] - 加载默认消息资源文件：classpath*:message/messages-*.properties
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,433][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'messageSource' of type [org.springframework.context.support.ReloadableResourceBundleMessageSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][61896,1,main][2025-06-23 09:09:53,455][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'validator' of type [org.springframework.validation.beanvalidation.LocalValidatorFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][61896,1,main][2025-06-23 09:09:54,302][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'cacheConfig' of type [com.sinitek.sirm.config.CacheConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][61896,1,main][2025-06-23 09:09:55,810][org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize:108] - Tomcat initialized with port(s): 8097 (http)
[INFO][SIRM][61896,1,main][2025-06-23 09:09:56,172][org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.prepareWebApplicationContext:290] - Root WebApplicationContext: initialization completed in 6162 ms
[INFO][SIRM][61896,1,main][2025-06-23 09:09:57,497][com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure.dataSource:55] - Init DruidDataSource
[INFO][SIRM][61896,1,main][2025-06-23 09:09:59,485][com.alibaba.druid.pool.DruidDataSource.init:972] - {dataSource-1} inited
[INFO][SIRM][61896,1,main][2025-06-23 09:10:04,026][com.sinitek.sirm.nocode.support.autoconfigure.FlywayAutoConfiguration.<init>:38] - flyway jdbcUrl: jdbc:postgresql://**************:25432/lowcode_saas_10_dev?useUnicode=true&characterEncoding=utf8&currentSchema=public&stringtype=unspecified
[INFO][SIRM][61896,1,main][2025-06-23 09:10:05,035][com.sinitek.sirm.ip.limit.interceptor.IpWhiteListInterceptor.init:56] - 当前ip限制状态为: false, true为开启状态,false为关闭状态
[INFO][SIRM][61896,1,main][2025-06-23 09:10:05,707][com.sinitek.sirm.config.SpringMessageConfig.localeChangeInterceptor:123] - 国际化开启状态: false, 系统默认的语言环境为: zh_CN,当前系统支持的语言环境列表: [LocaleInfo(locale=zh_CN, localeName=简体中文, localeChineseName=简体中文), LocaleInfo(locale=zh_HK, localeName=繁體中文, localeChineseName=繁体中文)]
[INFO][SIRM][61896,1,main][2025-06-23 09:10:05,984][springfox.documentation.spring.web.WebMvcPropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69] - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
[INFO][SIRM][61896,1,main][2025-06-23 09:10:06,188][com.sinitek.cloud.base.config.CloudSiniCubeConfig.currentUserInfo:37] - 当前com.sinitek.sirm.common.user.bridging.ICurrentUserInfo的实现类为: com.sinitek.cloud.base.user.CloudServerCurrentUserInfoImpl
[INFO][SIRM][61896,1,main][2025-06-23 09:10:07,909][com.sinitek.sirm.nocode.support.autoconfigure.FlywayAutoConfiguration.lambda$cleanMigrateStrategy$0:46] - 开始检查有没有执行错误的脚本，如果有则先清理掉
[INFO][SIRM][61896,1,main][2025-06-23 09:10:07,925][com.zaxxer.hikari.HikariDataSource.getConnection:110] - HikariPool-1 - Starting...
[INFO][SIRM][61896,1,main][2025-06-23 09:10:08,054][com.zaxxer.hikari.HikariDataSource.getConnection:123] - HikariPool-1 - Start completed.
[INFO][SIRM][61896,1,main][2025-06-23 09:10:08,086][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Flyway Community Edition 8.0.5 by Redgate
[INFO][SIRM][61896,1,main][2025-06-23 09:10:08,086][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Database: jdbc:postgresql://**************:25432/lowcode_saas_10_dev (PostgreSQL 13.2)
[INFO][SIRM][61896,1,main][2025-06-23 09:10:08,370][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Repair of failed migration in Schema History table "public"."flyway_schema_history" not necessary. No failed migration detected.
[INFO][SIRM][61896,1,main][2025-06-23 09:10:08,427][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Repairing Schema History table for version "100.20250619.01" (Marking as DELETED)  ...
[INFO][SIRM][61896,1,main][2025-06-23 09:10:08,567][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Successfully repaired schema history table "public"."flyway_schema_history" (execution time 00:00.252s).
[INFO][SIRM][61896,1,main][2025-06-23 09:10:08,568][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Please ensure the previous contents of the deleted migrations are removed from the database, or moved into an existing migration.
[INFO][SIRM][61896,1,main][2025-06-23 09:10:08,734][com.sinitek.sirm.nocode.support.autoconfigure.FlywayAutoConfiguration.lambda$cleanMigrateStrategy$0:48] - 开始执行SQL脚本
[INFO][SIRM][61896,1,main][2025-06-23 09:10:08,759][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Flyway Community Edition 8.0.5 by Redgate
[INFO][SIRM][61896,1,main][2025-06-23 09:10:08,963][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Current version of schema "public": 100.20250605.01
[INFO][SIRM][61896,1,main][2025-06-23 09:10:08,974][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Schema "public" is up to date. No migration necessary.
[INFO][SIRM][61896,1,main][2025-06-23 09:10:09,475][com.sinitek.sirm.nocode.support.autoconfigure.FlywayAutoConfiguration.close:56] - 关闭flyway数据源
[INFO][SIRM][61896,1,main][2025-06-23 09:10:09,476][com.zaxxer.hikari.HikariDataSource.close:350] - HikariPool-1 - Shutdown initiated...
[INFO][SIRM][61896,1,main][2025-06-23 09:10:09,490][com.zaxxer.hikari.HikariDataSource.close:352] - HikariPool-1 - Shutdown completed.
[INFO][SIRM][61896,1,main][2025-06-23 09:10:09,553][org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start:220] - Tomcat started on port(s): 8097 (http) with context path '/zhida'
[INFO][SIRM][61896,1,main][2025-06-23 09:10:09,581][com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO][SIRM][61896,1,main][2025-06-23 09:10:09,582][com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO][SIRM][61896,1,main][2025-06-23 09:10:09,929][com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register:76] - nacos registry, DEFAULT_GROUP SINITEK-NOCODE-BACKEND **************:8097 register finished
[INFO][SIRM][61896,1,main][2025-06-23 09:10:09,944][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:93] - Documentation plugins bootstrapped
[INFO][SIRM][61896,1,main][2025-06-23 09:10:09,951][springfox.documentation.spring.web.plugins.AbstractDocumentationPluginsBootstrapper.bootstrapDocumentationPlugins:79] - Found 2 custom documentation plugin(s)
[INFO][SIRM][61896,1,main][2025-06-23 09:10:09,994][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:44] - Scanning for api listing references
[INFO][SIRM][61896,1,main][2025-06-23 09:10:10,137][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: findOpenApiListUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:10,313][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveUsingPOST_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:10,655][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteUsingPOST_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:10,673][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: updateNameUsingPOST_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:10,681][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: customUrlUsingPOST_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:10,754][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveOrUpdateUsingPOST_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:10,829][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: viewUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:10,848][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteUsingPOST_2
[INFO][SIRM][61896,1,main][2025-06-23 09:10:10,855][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteBatchUsingPOST_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:10,859][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveOrUpdateUsingPOST_2
[INFO][SIRM][61896,1,main][2025-06-23 09:10:10,874][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: listUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:10,905][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: searchUsingPOST_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,069][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:44] - Scanning for api listing references
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,094][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveUsingPOST_2
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,102][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveOrUpdateUsingPOST_3
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,108][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: platformParamUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,110][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: loginUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,123][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getPageAuthUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,133][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getBaseSettingUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,138][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getInfoByCustomUrlUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,154][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveUsingPOST_3
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,162][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveGroupUsingPOST_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,169][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getPageSceneUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,174][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteUsingPOST_3
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,176][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteBatchUsingPOST_2
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,180][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deletePageAuthUsingPOST_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,183][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveBaseSettingUsingPOST_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,193][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveOrUpdatePageSceneUsingPOST_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,195][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: updateNameUsingPOST_2
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,210][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: copyPageAuthUsingPOST_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,231][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveOrUpdatePageAuthUsingPOST_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,236][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: customUrlUsingPOST_2
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,237][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getFieldAuthDataUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,239][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getFirstFormCodeUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,240][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getNameByCodeUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,240][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getUrlByCodeUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,255][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: sceneTypeListUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,259][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: groupListUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,269][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: groupTreeUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,271][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: listUsingGET_2
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,272][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: moveUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,284][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getAllFormUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,301][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: searchPageAuthListUsingPOST_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,308][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getBaseInfoUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,315][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: viewSecretUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,341][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: settingUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,343][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteUsingPOST_4
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,349][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: updateUsingPOST_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,357][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: updateNameUsingPOST_3
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,358][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: updateStatusUsingPOST_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,360][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: createUsingPOST_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,368][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: customUrlUsingPOST_3
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,379][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: searchUsingPOST_2
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,387][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: accessTokenUsingPOST_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,396][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getEnumerateUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,414][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getManageFormButtonsUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,420][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: applyHistoryVersionUsingPOST_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,421][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getFormPageDataUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,424][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getPublishedFormUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,427][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: updateVersionUsingPOST_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,429][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: viewUsingGET_2
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,433][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveOrUpdateUsingPOST_4
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,440][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getIdleTableLengthUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,445][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: effectVersionUsingPOST_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,447][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: publishUsingPOST_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,454][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveFiledConfigUsingPOST_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,460][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getFormDataManageSettingUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,466][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: versionHistoryUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,472][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: updateHistoryUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,484][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getFieldMappingCodeUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,495][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getTemporaryUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,496][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: viewUsingGET_3
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,503][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: pageFormDataFillReportUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,508][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteUsingPOST_5
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,516][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteBatchUsingPOST_3
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,525][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveOrUpdateUsingPOST_5
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,527][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: temporarySaveUsingPOST_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,533][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: listUsingGET_3
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,569][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: searchUsingPOST_3
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,578][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: exportUsingPOST_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,589][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: agentGenerateFunctionUsingPOST_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,611][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingGET_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,618][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingHEAD_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,623][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingPOST_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,627][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingPUT_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,632][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingPATCH_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,635][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingDELETE_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,638][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingOPTIONS_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,640][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingTRACE_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,644][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: agentGenerateDiagramUsingPOST_1
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,668][org.springframework.boot.StartupInfoLogger.logStarted:61] - Started SirmApplication in 25.13 seconds (JVM running for 26.632)
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,681][com.sinitek.sirm.nocode.common.support.swagger.SwaggerJsonUrl.lambda$onApplicationEvent$0:45] - 
----------------------------------------------------------
	 swaggerJson地址 http://***********:8097/zhida/v2/api-docs
----------------------------------------------------------
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,681][com.sinitek.sirm.nocode.common.support.swagger.SwaggerJsonUrl.lambda$onApplicationEvent$0:45] - 
----------------------------------------------------------
	 swaggerJson地址 http://***********:8097/zhida/v2/api-docs?group=零代码平台
----------------------------------------------------------
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,686][com.sinitek.data.mybatis.init.EntityNameApplicationRunner.run:68] - 全局实体EntityName初始化完成,总匹配实体数量为: 0,自动设置实体数量为: 0, 请不要在当前代码执行前使用实体.ENTITY_NAME,启动之前可以使用 new 实体名().getEntityNameValue()方式获取到EntityName
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,686][com.sinitek.sirm.common.encryption.init.EncryptionAlgorithmCheckRunner.printAlgorithmDescription:107] - 当前使用的 对称加密 算法为: AES, 实现类 为: com.sinitek.sirm.common.encryption.algorithm.symmetry.impl.AesSymmetryEncryptionImpl,该类是框架默认实现
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,686][com.sinitek.sirm.common.encryption.init.EncryptionAlgorithmCheckRunner.printAlgorithmDescription:107] - 当前使用的 非对称加密 算法为: RSA, 实现类 为: com.sinitek.sirm.common.encryption.algorithm.asymmetric.impl.RsaAsymmetricEncryptionImpl,该类是框架默认实现
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,687][com.sinitek.sirm.common.encryption.init.EncryptionAlgorithmCheckRunner.printAlgorithmDescription:107] - 当前使用的 散列 算法为: MD5, 实现类 为: com.sinitek.sirm.common.encryption.algorithm.hash.impl.MD5HashEncryptionImpl,该类是框架默认实现
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,725][com.sinitek.sirm.common.init.SiniCubeUtilsRunner.run:22] - 向ConvertUtils注册DateTimeConverter完成
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,746][com.alibaba.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:131] - [Nacos Config] Listening config: dataId=sinitek-nocode-backend-local.yml, group=DEFAULT_GROUP
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,750][com.alibaba.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:131] - [Nacos Config] Listening config: dataId=sinitek-nocode-backend.yml, group=DEFAULT_GROUP
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,751][com.alibaba.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:131] - [Nacos Config] Listening config: dataId=sinitek-nocode-backend, group=DEFAULT_GROUP
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,802][com.sinitek.cloud.common.cache.IPCache.getLocalIp:56] - 当前服务器的IPv4地址为: ***********
[INFO][SIRM][61896,98,temp-dir-cleaner-thread][2025-06-23 09:10:11,810][com.sinitek.sirm.common.tempdir.support.TempDirCleaner.run:55] - 自动清理临时目录功能启用状态：false
[INFO][SIRM][61896,1,main][2025-06-23 09:10:11,853][com.sinitek.sirm.nocode.utils.ApplicationStartUtil.startBackendApplication:29] - 
----------------------------------------------------------
	 http://***********:8097/zhida 运行成功
----------------------------------------------------------
[INFO][SIRM][61896,94,SinicubeThreadExecutor-1][2025-06-23 09:10:12,575][com.sinitek.data.model.recheck.support.RecheckSupport.createInstance:39] - 成功初始化复核模型，加载了0个复核实体定义
[INFO][SIRM][61896,67,http-nio-8097-exec-2][2025-06-23 09:10:38,982][org.springframework.web.servlet.FrameworkServlet.initServletBean:525] - Initializing Servlet 'dispatcherServlet'
[INFO][SIRM][61896,67,http-nio-8097-exec-2][2025-06-23 09:10:38,990][org.springframework.web.servlet.FrameworkServlet.initServletBean:547] - Completed initialization in 7 ms
[INFO][SIRM][61896,67,http-nio-8097-exec-2][2025-06-23 09:10:39,552][com.sinitek.sirm.nocode.controller.ZdLlmController.agentGenerateDiagram:133] - 开始生成图表，表单代码: page_36f15be5f7af4f43a8229d8703e05133, 需求: 统计考试成绩，语文和数学的平均分。用柱状图表示
[INFO][SIRM][61896,67,http-nio-8097-exec-2][2025-06-23 09:10:39,555][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.formSqlGenerate:343] - 开始生成SQL，表单代码: page_36f15be5f7af4f43a8229d8703e05133, 需求: 统计考试成绩，语文和数学的平均分。用柱状图表示
[INFO][SIRM][61896,67,http-nio-8097-exec-2][2025-06-23 09:11:24,394][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.formSqlGenerate:353] - SQL生成完成，表单代码: page_36f15be5f7af4f43a8229d8703e05133, SQL长度: 407
[INFO][SIRM][61896,67,http-nio-8097-exec-2][2025-06-23 09:11:24,401][com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery:84] - 执行动态SQL查询: SELECT 
    subject_data ->> 'ZDInput_jvy2' AS subject,
    AVG((score_data ->> 'ZDNumber_wx80')::numeric) AS avg_score
FROM 
    zd_page_form_data_64,
    jsonb_array_elements(form_data -> 'model' -> 'ZDChildForm_zolh') AS subject_data,
    jsonb_array_elements(subject_data -> 'model') AS score_data
WHERE 
    subject_data ->> 'ZDInput_jvy2' IN ('语文', '数学')
GROUP BY 
    subject_data ->> 'ZDInput_jvy2';
[INFO][SIRM][61896,67,http-nio-8097-exec-2][2025-06-23 09:11:24,459][com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery:102] - 查询完成，返回0条记录
[INFO][SIRM][61896,67,http-nio-8097-exec-2][2025-06-23 09:11:24,490][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.mermaidGenerate:475] - 开始生成Mermaid图表，用户需求: 需求：统计考试成绩，语文和数学的平均分。用柱状图表示 

数据：[] 

请根据上面的需求和数据生成mermaid代码。
[INFO][SIRM][61896,67,http-nio-8097-exec-2][2025-06-23 09:11:30,838][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.mermaidGenerate:486] - Mermaid图表生成完成，代码长度: 78
[INFO][SIRM][61896,67,http-nio-8097-exec-2][2025-06-23 09:11:30,840][com.sinitek.sirm.nocode.controller.ZdLlmController.executeGenerateDiagram:190] - 图表生成完成，会话ID: null
[INFO][SIRM][61896,73,http-nio-8097-exec-8][2025-06-23 09:25:52,528][com.sinitek.sirm.nocode.controller.ZdLlmController.agentGenerateDiagram:133] - 开始生成图表，表单代码: page_36f15be5f7af4f43a8229d8703e05133, 需求: 分析考试成绩里，语文和数学的平均分，用柱状图表达
[INFO][SIRM][61896,73,http-nio-8097-exec-8][2025-06-23 09:25:52,531][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.formSqlGenerate:343] - 开始生成SQL，表单代码: page_36f15be5f7af4f43a8229d8703e05133, 需求: 分析考试成绩里，语文和数学的平均分，用柱状图表达
[INFO][SIRM][61896,73,http-nio-8097-exec-8][2025-06-23 09:26:26,619][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.formSqlGenerate:353] - SQL生成完成，表单代码: page_36f15be5f7af4f43a8229d8703e05133, SQL长度: 647
[INFO][SIRM][61896,73,http-nio-8097-exec-8][2025-06-23 09:26:26,623][com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery:84] - 执行动态SQL查询: SELECT 
    subject,
    ROUND(AVG(score), 2) AS avg_score
FROM (
    SELECT 
        child->>'ZDInput_jvy2' AS subject,
        (child->>'ZDNumber_wx80')::FLOAT AS score
    FROM 
        zd_page_form_data_64,
        jsonb_array_elements(
            CASE 
                WHEN jsonb_typeof(form_data->'model'->'ZDChildForm_zolh') = 'array' 
                THEN form_data->'model'->'ZDChildForm_zolh'
                ELSE '[]'::jsonb 
            END
        ) AS child
    WHERE 
        child->>'ZDInput_jvy2' IN ('语文', '数学')
        AND child->>'ZDNumber_wx80' ~ '^\d+(\.\d+)?$'
) AS valid_scores
GROUP BY 
    subject
ORDER BY 
    subject;
[ERROR][SIRM][61896,73,http-nio-8097-exec-8][2025-06-23 09:26:54,771][com.alibaba.druid.filter.logging.Log4jFilter.statementLogError:151] - {conn-10012, pstmt-20035} execute error. SELECT 
    subject,
    ROUND(AVG(score), 2) AS avg_score
FROM (
    SELECT 
        child->>'ZDInput_jvy2' AS subject,
        (child->>'ZDNumber_wx80')::FLOAT AS score
    FROM 
        zd_page_form_data_64,
        jsonb_array_elements(
            CASE 
                WHEN jsonb_typeof(form_data->'model'->'ZDChildForm_zolh') = 'array' 
                THEN form_data->'model'->'ZDChildForm_zolh'
                ELSE '[]'::jsonb 
            END
        ) AS child
    WHERE 
        child->>'ZDInput_jvy2' IN ('语文', '数学')
        AND child->>'ZDNumber_wx80' ~ '^\d+(\.\d+)?$'
) AS valid_scores
GROUP BY 
    subject
ORDER BY 
    subject;
org.postgresql.util.PSQLException: ERROR: function round(double precision, integer) does not exist
  建议：No function matches the given name and argument types. You might need to add explicit type casts.
  位置：26
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2674) ~[postgresql-42.3.1.jar:42.3.1]
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2364) ~[postgresql-42.3.1.jar:42.3.1]
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:354) ~[postgresql-42.3.1.jar:42.3.1]
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:484) ~[postgresql-42.3.1.jar:42.3.1]
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:404) ~[postgresql-42.3.1.jar:42.3.1]
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:162) ~[postgresql-42.3.1.jar:42.3.1]
	at org.postgresql.jdbc.PgPreparedStatement.execute(PgPreparedStatement.java:151) ~[postgresql-42.3.1.jar:42.3.1]
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3446) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.wall.WallFilter.preparedStatement_execute(WallFilter.java:638) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:152) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483) ~[druid-1.2.11.jar:1.2.11]
	at sun.reflect.GeneratedMethodAccessor76.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy275.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79) ~[mybatis-3.5.6.jar:3.5.6]
	at sun.reflect.GeneratedMethodAccessor65.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy274.query(Unknown Source) ~[?:?]
	at sun.reflect.GeneratedMethodAccessor65.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy274.query(Unknown Source) ~[?:?]
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doQuery(MybatisSimpleExecutor.java:69) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:165) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:92) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at sun.reflect.GeneratedMethodAccessor73.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy273.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.6.jar:3.5.6]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at com.sun.proxy.$Proxy140.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:223) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:173) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:78) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.sun.proxy.$Proxy141.executeDynamicQuery(Unknown Source) ~[?:?]
	at com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery(SqlQueryUtil.java:91) ~[classes/:?]
	at com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery(SqlQueryUtil.java:63) ~[classes/:?]
	at com.sinitek.sirm.nocode.controller.ZdLlmController.executeGenerateDiagram(ZdLlmController.java:155) ~[classes/:?]
	at com.sinitek.sirm.nocode.controller.ZdLlmController.agentGenerateDiagram(ZdLlmController.java:135) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.github.xiaoymin.knife4j.spring.filter.SecurityBasicAuthFilter.doFilter(SecurityBasicAuthFilter.java:87) ~[knife4j-spring-2.0.8.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.11.jar:1.2.11]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
[ERROR][SIRM][61896,73,http-nio-8097-exec-8][2025-06-23 09:26:55,002][com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery:106] - 执行SQL查询失败: SELECT 
    subject,
    ROUND(AVG(score), 2) AS avg_score
FROM (
    SELECT 
        child->>'ZDInput_jvy2' AS subject,
        (child->>'ZDNumber_wx80')::FLOAT AS score
    FROM 
        zd_page_form_data_64,
        jsonb_array_elements(
            CASE 
                WHEN jsonb_typeof(form_data->'model'->'ZDChildForm_zolh') = 'array' 
                THEN form_data->'model'->'ZDChildForm_zolh'
                ELSE '[]'::jsonb 
            END
        ) AS child
    WHERE 
        child->>'ZDInput_jvy2' IN ('语文', '数学')
        AND child->>'ZDNumber_wx80' ~ '^\d+(\.\d+)?$'
) AS valid_scores
GROUP BY 
    subject
ORDER BY 
    subject;
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: function round(double precision, integer) does not exist
  建议：No function matches the given name and argument types. You might need to add explicit type casts.
  位置：26
### The error may exist in com/sinitek/sirm/nocode/common/mapper/CommonMapper.xml
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT      subject,     ROUND(AVG(score), 2) AS avg_score FROM (     SELECT          child->>'ZDInput_jvy2' AS subject,         (child->>'ZDNumber_wx80')::FLOAT AS score     FROM          zd_page_form_data_64,         jsonb_array_elements(             CASE                  WHEN jsonb_typeof(form_data->'model'->'ZDChildForm_zolh') = 'array'                  THEN form_data->'model'->'ZDChildForm_zolh'                 ELSE '[]'::jsonb              END         ) AS child     WHERE          child->>'ZDInput_jvy2' IN ('语文', '数学')         AND child->>'ZDNumber_wx80' ~ '^\d+(\.\d+)?$' ) AS valid_scores GROUP BY      subject ORDER BY      subject;
### Cause: org.postgresql.util.PSQLException: ERROR: function round(double precision, integer) does not exist
  建议：No function matches the given name and argument types. You might need to add explicit type casts.
  位置：26
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: function round(double precision, integer) does not exist
  建议：No function matches the given name and argument types. You might need to add explicit type casts.
  位置：26
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:101) ~[spring-jdbc-5.3.39.jar:5.3.39]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73) ~[spring-jdbc-5.3.39.jar:5.3.39]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.39.jar:5.3.39]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.39.jar:5.3.39]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:88) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at com.sun.proxy.$Proxy140.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:223) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:173) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:78) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.sun.proxy.$Proxy141.executeDynamicQuery(Unknown Source) ~[?:?]
	at com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery(SqlQueryUtil.java:91) ~[classes/:?]
	at com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery(SqlQueryUtil.java:63) ~[classes/:?]
	at com.sinitek.sirm.nocode.controller.ZdLlmController.executeGenerateDiagram(ZdLlmController.java:155) ~[classes/:?]
	at com.sinitek.sirm.nocode.controller.ZdLlmController.agentGenerateDiagram(ZdLlmController.java:135) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.github.xiaoymin.knife4j.spring.filter.SecurityBasicAuthFilter.doFilter(SecurityBasicAuthFilter.java:87) ~[knife4j-spring-2.0.8.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.11.jar:1.2.11]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: org.postgresql.util.PSQLException: ERROR: function round(double precision, integer) does not exist
  建议：No function matches the given name and argument types. You might need to add explicit type casts.
  位置：26
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2674) ~[postgresql-42.3.1.jar:42.3.1]
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2364) ~[postgresql-42.3.1.jar:42.3.1]
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:354) ~[postgresql-42.3.1.jar:42.3.1]
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:484) ~[postgresql-42.3.1.jar:42.3.1]
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:404) ~[postgresql-42.3.1.jar:42.3.1]
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:162) ~[postgresql-42.3.1.jar:42.3.1]
	at org.postgresql.jdbc.PgPreparedStatement.execute(PgPreparedStatement.java:151) ~[postgresql-42.3.1.jar:42.3.1]
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3446) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.wall.WallFilter.preparedStatement_execute(WallFilter.java:638) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:152) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483) ~[druid-1.2.11.jar:1.2.11]
	at sun.reflect.GeneratedMethodAccessor76.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy275.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79) ~[mybatis-3.5.6.jar:3.5.6]
	at sun.reflect.GeneratedMethodAccessor65.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy274.query(Unknown Source) ~[?:?]
	at sun.reflect.GeneratedMethodAccessor65.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy274.query(Unknown Source) ~[?:?]
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doQuery(MybatisSimpleExecutor.java:69) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:165) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:92) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at sun.reflect.GeneratedMethodAccessor73.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy273.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.6.jar:3.5.6]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426) ~[mybatis-spring-2.0.5.jar:2.0.5]
	... 67 more
[WARN][SIRM][61896,73,http-nio-8097-exec-8][2025-06-23 09:26:55,029][com.sinitek.sirm.nocode.controller.ZdLlmController.executeGenerateDiagram:160] - SQL执行出现P异常，准备重试。错误信息: SQL查询执行失败: 
### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: function round(double precision, integer) does not exist
  建议：No function matches the given name and argument types. You might need to add explicit type casts.
  位置：26
### The error may exist in com/sinitek/sirm/nocode/common/mapper/CommonMapper.xml
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT      subject,     ROUND(AVG(score), 2) AS avg_score FROM (     SELECT          child->>'ZDInput_jvy2' AS subject,         (child->>'ZDNumber_wx80')::FLOAT AS score     FROM          zd_page_form_data_64,         jsonb_array_elements(             CASE                  WHEN jsonb_typeof(form_data->'model'->'ZDChildForm_zolh') = 'array'                  THEN form_data->'model'->'ZDChildForm_zolh'                 ELSE '[]'::jsonb              END         ) AS child     WHERE          child->>'ZDInput_jvy2' IN ('语文', '数学')         AND child->>'ZDNumber_wx80' ~ '^\d+(\.\d+)?$' ) AS valid_scores GROUP BY      subject ORDER BY      subject;
### Cause: org.postgresql.util.PSQLException: ERROR: function round(double precision, integer) does not exist
  建议：No function matches the given name and argument types. You might need to add explicit type casts.
  位置：26
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: function round(double precision, integer) does not exist
  建议：No function matches the given name and argument types. You might need to add explicit type casts.
  位置：26
[INFO][SIRM][61896,73,http-nio-8097-exec-8][2025-06-23 09:26:55,030][com.sinitek.sirm.nocode.controller.ZdLlmController.executeGenerateDiagram:174] - 开始重试图表生成，增强后的需求: 分析考试成绩里，语文和数学的平均分，用柱状图表达

上次SQL执行出现错误：SQL查询执行失败: 
### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: function round(double precision, integer) does not exist
  建议：No function matches the given name and argument types. You might need to add explicit type casts.
  位置：26
### The error may exist in com/sinitek/sirm/nocode/common/mapper/CommonMapper.xml
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT      subject,     ROUND(AVG(score), 2) AS avg_score FROM (     SELECT          child->>'ZDInput_jvy2' AS subject,         (child->>'ZDNumber_wx80')::FLOAT AS score     FROM          zd_page_form_data_64,         jsonb_array_elements(             CASE                  WHEN jsonb_typeof(form_data->'model'->'ZDChildForm_zolh') = 'array'                  THEN form_data->'model'->'ZDChildForm_zolh'                 ELSE '[]'::jsonb              END         ) AS child     WHERE          child->>'ZDInput_jvy2' IN ('语文', '数学')         AND child->>'ZDNumber_wx80' ~ '^\d+(\.\d+)?$' ) AS valid_scores GROUP BY      subject ORDER BY      subject;
### Cause: org.postgresql.util.PSQLException: ERROR: function round(double precision, integer) does not exist
  建议：No function matches the given name and argument types. You might need to add explicit type casts.
  位置：26
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: function round(double precision, integer) does not exist
  建议：No function matches the given name and argument types. You might need to add explicit type casts.
  位置：26
请根据错误信息调整SQL语句。
[INFO][SIRM][61896,73,http-nio-8097-exec-8][2025-06-23 09:27:27,894][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.formSqlGenerate:343] - 开始生成SQL，表单代码: page_36f15be5f7af4f43a8229d8703e05133, 需求: 分析考试成绩里，语文和数学的平均分，用柱状图表达

上次SQL执行出现错误：SQL查询执行失败: 
### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: function round(double precision, integer) does not exist
  建议：No function matches the given name and argument types. You might need to add explicit type casts.
  位置：26
### The error may exist in com/sinitek/sirm/nocode/common/mapper/CommonMapper.xml
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT      subject,     ROUND(AVG(score), 2) AS avg_score FROM (     SELECT          child->>'ZDInput_jvy2' AS subject,         (child->>'ZDNumber_wx80')::FLOAT AS score     FROM          zd_page_form_data_64,         jsonb_array_elements(             CASE                  WHEN jsonb_typeof(form_data->'model'->'ZDChildForm_zolh') = 'array'                  THEN form_data->'model'->'ZDChildForm_zolh'                 ELSE '[]'::jsonb              END         ) AS child     WHERE          child->>'ZDInput_jvy2' IN ('语文', '数学')         AND child->>'ZDNumber_wx80' ~ '^\d+(\.\d+)?$' ) AS valid_scores GROUP BY      subject ORDER BY      subject;
### Cause: org.postgresql.util.PSQLException: ERROR: function round(double precision, integer) does not exist
  建议：No function matches the given name and argument types. You might need to add explicit type casts.
  位置：26
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: function round(double precision, integer) does not exist
  建议：No function matches the given name and argument types. You might need to add explicit type casts.
  位置：26
请根据错误信息调整SQL语句。
[INFO][SIRM][61896,73,http-nio-8097-exec-8][2025-06-23 09:27:46,037][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.formSqlGenerate:353] - SQL生成完成，表单代码: page_36f15be5f7af4f43a8229d8703e05133, SQL长度: 652
[INFO][SIRM][61896,73,http-nio-8097-exec-8][2025-06-23 09:27:46,042][com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery:84] - 执行动态SQL查询: SELECT
    subject,
    ROUND(AVG(score::numeric), 2) AS avg_score
FROM (
    SELECT
        child->>'ZDInput_jvy2' AS subject,
        (child->>'ZDNumber_wx80')::numeric AS score
    FROM
        zd_page_form_data_64,
        jsonb_array_elements(
            CASE 
                WHEN jsonb_typeof(form_data->'model'->'ZDChildForm_zolh') = 'array' 
                THEN form_data->'model'->'ZDChildForm_zolh'
                ELSE '[]'::jsonb 
            END
        ) AS child
    WHERE
        child->>'ZDInput_jvy2' IN ('语文', '数学')
        AND child->>'ZDNumber_wx80' ~ '^\d+(\.\d+)?$'
) AS valid_scores
GROUP BY
    subject
ORDER BY
    subject;
[INFO][SIRM][61896,73,http-nio-8097-exec-8][2025-06-23 09:27:46,098][com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery:102] - 查询完成，返回2条记录
[INFO][SIRM][61896,73,http-nio-8097-exec-8][2025-06-23 09:27:46,114][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.mermaidGenerate:475] - 开始生成Mermaid图表，用户需求: 需求：分析考试成绩里，语文和数学的平均分，用柱状图表达

上次SQL执行出现错误：SQL查询执行失败: 
### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: function round(double precision, integer) does not exist
  建议：No function matches the given name and argument types. You might need to add explicit type casts.
  位置：26
### The error may exist in com/sinitek/sirm/nocode/common/mapper/CommonMapper.xml
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT      subject,     ROUND(AVG(score), 2) AS avg_score FROM (     SELECT          child->>'ZDInput_jvy2' AS subject,         (child->>'ZDNumber_wx80')::FLOAT AS score     FROM          zd_page_form_data_64,         jsonb_array_elements(             CASE                  WHEN jsonb_typeof(form_data->'model'->'ZDChildForm_zolh') = 'array'                  THEN form_data->'model'->'ZDChildForm_zolh'                 ELSE '[]'::jsonb              END         ) AS child     WHERE          child->>'ZDInput_jvy2' IN ('语文', '数学')         AND child->>'ZDNumber_wx80' ~ '^\d+(\.\d+)?$' ) AS valid_scores GROUP BY      subject ORDER BY      subject;
### Cause: org.postgresql.util.PSQLException: ERROR: function round(double precision, integer) does not exist
  建议：No function matches the given name and argument types. You might need to add explicit type casts.
  位置：26
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: function round(double precision, integer) does not exist
  建议：No function matches the given name and argument types. You might need to add explicit type casts.
  位置：26
请根据错误信息调整SQL语句。 

数据：[{"avgScore":111,"subject":"数学"},{"avgScore":115,"subject":"语文"}] 

请根据上面的需求和数据生成mermaid代码。
[INFO][SIRM][61896,73,http-nio-8097-exec-8][2025-06-23 09:28:00,728][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.mermaidGenerate:486] - Mermaid图表生成完成，代码长度: 105
[INFO][SIRM][61896,73,http-nio-8097-exec-8][2025-06-23 09:28:00,730][com.sinitek.sirm.nocode.controller.ZdLlmController.executeGenerateDiagram:190] - 图表生成完成，会话ID: null
[INFO][SIRM][61896,74,http-nio-8097-exec-9][2025-06-23 09:46:24,261][com.sinitek.sirm.nocode.controller.ZdLlmController.agentGenerateDiagram:133] - 开始生成图表，表单代码: page_36f15be5f7af4f43a8229d8703e05133, 需求: 分析考试成绩里，语文和数学的平均分，用柱状图表达
[INFO][SIRM][61896,74,http-nio-8097-exec-9][2025-06-23 09:46:24,265][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.formSqlGenerate:343] - 开始生成SQL，表单代码: page_36f15be5f7af4f43a8229d8703e05133, 需求: 分析考试成绩里，语文和数学的平均分，用柱状图表达
[INFO][SIRM][61896,74,http-nio-8097-exec-9][2025-06-23 09:47:05,402][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.formSqlGenerate:353] - SQL生成完成，表单代码: page_36f15be5f7af4f43a8229d8703e05133, SQL长度: 364
[INFO][SIRM][61896,74,http-nio-8097-exec-9][2025-06-23 09:47:05,406][com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery:84] - 执行动态SQL查询: SELECT 
    subject,
    AVG(score) AS avg_score
FROM (
    SELECT 
        (child->'model'->>'ZDInput_jvy2') AS subject,
        (child->'model'->>'ZDNumber_wx80')::NUMERIC AS score
    FROM 
        zd_page_form_data_64,
        jsonb_array_elements(form_data->'model'->'ZDChildForm_zolh') AS child
) sub
WHERE 
    subject IN ('语文', '数学')
GROUP BY 
    subject;
[INFO][SIRM][61896,74,http-nio-8097-exec-9][2025-06-23 09:47:05,438][com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery:102] - 查询完成，返回0条记录
[INFO][SIRM][61896,74,http-nio-8097-exec-9][2025-06-23 09:47:05,439][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.mermaidGenerate:475] - 开始生成Mermaid图表，用户需求: 需求：分析考试成绩里，语文和数学的平均分，用柱状图表达 

数据：[] 

请根据上面的需求和数据生成mermaid代码。
[INFO][SIRM][61896,74,http-nio-8097-exec-9][2025-06-23 09:47:14,012][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.mermaidGenerate:486] - Mermaid图表生成完成，代码长度: 100
[INFO][SIRM][61896,74,http-nio-8097-exec-9][2025-06-23 09:47:14,015][com.sinitek.sirm.nocode.controller.ZdLlmController.executeGenerateDiagram:190] - 图表生成完成，会话ID: null
[INFO][SIRM][61896,69,http-nio-8097-exec-4][2025-06-23 09:56:42,471][com.sinitek.sirm.nocode.controller.ZdLlmController.agentGenerateDiagram:133] - 开始生成图表，表单代码: page_36f15be5f7af4f43a8229d8703e05133, 需求: 分析考试成绩，语文和数学的平均分
[INFO][SIRM][61896,69,http-nio-8097-exec-4][2025-06-23 09:56:42,477][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.formSqlGenerate:343] - 开始生成SQL，表单代码: page_36f15be5f7af4f43a8229d8703e05133, 需求: 分析考试成绩，语文和数学的平均分
[INFO][SIRM][61896,69,http-nio-8097-exec-4][2025-06-23 09:58:27,610][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.formSqlGenerate:353] - SQL生成完成，表单代码: page_36f15be5f7af4f43a8229d8703e05133, SQL长度: 522
[INFO][SIRM][61896,69,http-nio-8097-exec-4][2025-06-23 09:58:27,617][com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery:84] - 执行动态SQL查询: SELECT
    subject_data->>'ZDInput_jvy2' AS subject,
    AVG((subject_data->>'ZDNumber_wx80')::numeric) AS avg_score
FROM
    zd_page_form_data_64,
    jsonb_array_elements(
        CASE 
            WHEN form_data->'model'->'ZDChildForm_zolh' IS NULL 
            THEN '[]'::jsonb 
            ELSE form_data->'model'->'ZDChildForm_zolh' 
        END
    ) AS subject_data
WHERE
    subject_data->>'ZDInput_jvy2' IN ('语文', '数学')
    AND subject_data->>'ZDNumber_wx80' ~ '^\d+$'
GROUP BY
    subject_data->>'ZDInput_jvy2';
[INFO][SIRM][61896,69,http-nio-8097-exec-4][2025-06-23 09:58:27,689][com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery:102] - 查询完成，返回2条记录
[INFO][SIRM][61896,69,http-nio-8097-exec-4][2025-06-23 09:58:27,692][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.mermaidGenerate:475] - 开始生成Mermaid图表，用户需求: 需求：分析考试成绩，语文和数学的平均分 

数据：[{"avgScore":111,"subject":"数学"},{"avgScore":115,"subject":"语文"}] 

请根据上面的需求和数据生成mermaid代码。
[INFO][SIRM][61896,69,http-nio-8097-exec-4][2025-06-23 09:59:26,889][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.mermaidGenerate:486] - Mermaid图表生成完成，代码长度: 107
[INFO][SIRM][61896,69,http-nio-8097-exec-4][2025-06-23 09:59:26,892][com.sinitek.sirm.nocode.controller.ZdLlmController.executeGenerateDiagram:191] - 图表生成完成，会话ID: null
[INFO][SIRM][61896,71,http-nio-8097-exec-6][2025-06-23 10:02:31,453][com.sinitek.sirm.nocode.controller.ZdLlmController.agentGenerateDiagram:133] - 开始生成图表，表单代码: page_36f15be5f7af4f43a8229d8703e05133, 需求: 分析考试成绩，语文和数学的平均分
[INFO][SIRM][61896,71,http-nio-8097-exec-6][2025-06-23 10:02:31,455][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.formSqlGenerate:343] - 开始生成SQL，表单代码: page_36f15be5f7af4f43a8229d8703e05133, 需求: 分析考试成绩，语文和数学的平均分
[INFO][SIRM][61896,71,http-nio-8097-exec-6][2025-06-23 10:04:06,212][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.formSqlGenerate:353] - SQL生成完成，表单代码: page_36f15be5f7af4f43a8229d8703e05133, SQL长度: 359
[INFO][SIRM][61896,71,http-nio-8097-exec-6][2025-06-23 10:04:06,216][com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery:84] - 执行动态SQL查询: SELECT 
    subject.item ->> 'ZDInput_jvy2' AS subject,
    AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score
FROM 
    zd_page_form_data_64,
    LATERAL jsonb_array_elements(form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item)
WHERE 
    subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学')
GROUP BY 
    subject.item ->> 'ZDInput_jvy2';
[ERROR][SIRM][61896,71,http-nio-8097-exec-6][2025-06-23 10:04:06,269][com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery:106] - 执行SQL查询失败: SELECT 
    subject.item ->> 'ZDInput_jvy2' AS subject,
    AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score
FROM 
    zd_page_form_data_64,
    LATERAL jsonb_array_elements(form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item)
WHERE 
    subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学')
GROUP BY 
    subject.item ->> 'ZDInput_jvy2';
org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.sql.SQLException: sql injection violation, dbType postgresql, , druid-version 1.2.11, syntax error: TODO pos 202, line 6, column 35, token IDENTIFIER form_data : SELECT 
    subject.item ->> 'ZDInput_jvy2' AS subject,
    AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score
FROM 
    zd_page_form_data_64,
    LATERAL jsonb_array_elements(form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item)
WHERE 
    subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学')
GROUP BY 
    subject.item ->> 'ZDInput_jvy2';
### The error may exist in com/sinitek/sirm/nocode/common/mapper/CommonMapper.xml
### The error may involve com.sinitek.sirm.nocode.common.mapper.CommonMapper.executeDynamicQuery
### The error occurred while executing a query
### SQL: SELECT      subject.item ->> 'ZDInput_jvy2' AS subject,     AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score FROM      zd_page_form_data_64,     LATERAL jsonb_array_elements(form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item) WHERE      subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学') GROUP BY      subject.item ->> 'ZDInput_jvy2';
### Cause: java.sql.SQLException: sql injection violation, dbType postgresql, , druid-version 1.2.11, syntax error: TODO pos 202, line 6, column 35, token IDENTIFIER form_data : SELECT 
    subject.item ->> 'ZDInput_jvy2' AS subject,
    AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score
FROM 
    zd_page_form_data_64,
    LATERAL jsonb_array_elements(form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item)
WHERE 
    subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学')
GROUP BY 
    subject.item ->> 'ZDInput_jvy2';
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:149) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.6.jar:3.5.6]
	at sun.reflect.GeneratedMethodAccessor189.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at com.sun.proxy.$Proxy140.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:223) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:173) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:78) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.sun.proxy.$Proxy141.executeDynamicQuery(Unknown Source) ~[?:?]
	at com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery(SqlQueryUtil.java:91) ~[classes/:?]
	at com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery(SqlQueryUtil.java:63) ~[classes/:?]
	at com.sinitek.sirm.nocode.controller.ZdLlmController.executeGenerateDiagram(ZdLlmController.java:155) ~[classes/:?]
	at com.sinitek.sirm.nocode.controller.ZdLlmController.agentGenerateDiagram(ZdLlmController.java:135) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.github.xiaoymin.knife4j.spring.filter.SecurityBasicAuthFilter.doFilter(SecurityBasicAuthFilter.java:87) ~[knife4j-spring-2.0.8.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.11.jar:1.2.11]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.sql.SQLException: sql injection violation, dbType postgresql, , druid-version 1.2.11, syntax error: TODO pos 202, line 6, column 35, token IDENTIFIER form_data : SELECT 
    subject.item ->> 'ZDInput_jvy2' AS subject,
    AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score
FROM 
    zd_page_form_data_64,
    LATERAL jsonb_array_elements(form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item)
WHERE 
    subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学')
GROUP BY 
    subject.item ->> 'ZDInput_jvy2';
	at com.alibaba.druid.wall.WallFilter.checkInternal(WallFilter.java:828) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.wall.WallFilter.connection_prepareStatement(WallFilter.java:270) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterChainImpl.connection_prepareStatement(FilterChainImpl.java:531) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterAdapter.connection_prepareStatement(FilterAdapter.java:908) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterEventAdapter.connection_prepareStatement(FilterEventAdapter.java:116) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterChainImpl.connection_prepareStatement(FilterChainImpl.java:531) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.proxy.jdbc.ConnectionProxyImpl.prepareStatement(ConnectionProxyImpl.java:326) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.pool.DruidPooledConnection.prepareStatement(DruidPooledConnection.java:362) ~[druid-1.2.11.jar:1.2.11]
	at sun.reflect.GeneratedMethodAccessor75.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.ConnectionLogger.invoke(ConnectionLogger.java:55) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy260.prepareStatement(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:86) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:88) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:59) ~[mybatis-3.5.6.jar:3.5.6]
	at sun.reflect.GeneratedMethodAccessor63.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor.intercept(PaginationInterceptor.java:193) ~[mybatis-plus-extension-3.4.1.jar:3.4.1]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy274.prepare(Unknown Source) ~[?:?]
	at sun.reflect.GeneratedMethodAccessor63.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sinitek.data.mybatis.plugins.CustomerFuncationInterceptor.intercept(CustomerFuncationInterceptor.java:84) ~[sinitek-data-mybatis-7.4.640.jar:7.4.640]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy274.prepare(Unknown Source) ~[?:?]
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.prepareStatement(MybatisSimpleExecutor.java:94) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doQuery(MybatisSimpleExecutor.java:68) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:165) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:92) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at sun.reflect.GeneratedMethodAccessor73.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy273.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.6.jar:3.5.6]
	... 72 more
Caused by: com.alibaba.druid.sql.parser.ParserException: TODO pos 202, line 6, column 35, token IDENTIFIER form_data
	at com.alibaba.druid.sql.parser.SQLStatementParser.parseStatementList(SQLStatementParser.java:578) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.sql.parser.SQLStatementParser.parseStatementList(SQLStatementParser.java:112) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.wall.WallProvider.checkInternal(WallProvider.java:618) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.wall.WallProvider.check(WallProvider.java:572) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.wall.WallFilter.checkInternal(WallFilter.java:805) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.wall.WallFilter.connection_prepareStatement(WallFilter.java:270) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterChainImpl.connection_prepareStatement(FilterChainImpl.java:531) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterAdapter.connection_prepareStatement(FilterAdapter.java:908) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterEventAdapter.connection_prepareStatement(FilterEventAdapter.java:116) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterChainImpl.connection_prepareStatement(FilterChainImpl.java:531) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.proxy.jdbc.ConnectionProxyImpl.prepareStatement(ConnectionProxyImpl.java:326) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.pool.DruidPooledConnection.prepareStatement(DruidPooledConnection.java:362) ~[druid-1.2.11.jar:1.2.11]
	at sun.reflect.GeneratedMethodAccessor75.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.ConnectionLogger.invoke(ConnectionLogger.java:55) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy260.prepareStatement(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:86) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:88) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:59) ~[mybatis-3.5.6.jar:3.5.6]
	at sun.reflect.GeneratedMethodAccessor63.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor.intercept(PaginationInterceptor.java:193) ~[mybatis-plus-extension-3.4.1.jar:3.4.1]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy274.prepare(Unknown Source) ~[?:?]
	at sun.reflect.GeneratedMethodAccessor63.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sinitek.data.mybatis.plugins.CustomerFuncationInterceptor.intercept(CustomerFuncationInterceptor.java:84) ~[sinitek-data-mybatis-7.4.640.jar:7.4.640]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy274.prepare(Unknown Source) ~[?:?]
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.prepareStatement(MybatisSimpleExecutor.java:94) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doQuery(MybatisSimpleExecutor.java:68) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:165) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:92) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at sun.reflect.GeneratedMethodAccessor73.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy273.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.6.jar:3.5.6]
	... 72 more
[WARN][SIRM][61896,71,http-nio-8097-exec-6][2025-06-23 10:04:06,300][com.sinitek.sirm.nocode.controller.ZdLlmController.executeGenerateDiagram:160] - SQL执行出现P异常，准备重试。错误信息: SQL查询执行失败: 
### Error querying database.  Cause: java.sql.SQLException: sql injection violation, dbType postgresql, , druid-version 1.2.11, syntax error: TODO pos 202, line 6, column 35, token IDENTIFIER form_data : SELECT 
    subject.item ->> 'ZDInput_jvy2' AS subject,
    AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score
FROM 
    zd_page_form_data_64,
    LATERAL jsonb_array_elements(form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item)
WHERE 
    subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学')
GROUP BY 
    subject.item ->> 'ZDInput_jvy2';
### The error may exist in com/sinitek/sirm/nocode/common/mapper/CommonMapper.xml
### The error may involve com.sinitek.sirm.nocode.common.mapper.CommonMapper.executeDynamicQuery
### The error occurred while executing a query
### SQL: SELECT      subject.item ->> 'ZDInput_jvy2' AS subject,     AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score FROM      zd_page_form_data_64,     LATERAL jsonb_array_elements(form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item) WHERE      subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学') GROUP BY      subject.item ->> 'ZDInput_jvy2';
### Cause: java.sql.SQLException: sql injection violation, dbType postgresql, , druid-version 1.2.11, syntax error: TODO pos 202, line 6, column 35, token IDENTIFIER form_data : SELECT 
    subject.item ->> 'ZDInput_jvy2' AS subject,
    AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score
FROM 
    zd_page_form_data_64,
    LATERAL jsonb_array_elements(form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item)
WHERE 
    subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学')
GROUP BY 
    subject.item ->> 'ZDInput_jvy2';
[INFO][SIRM][61896,71,http-nio-8097-exec-6][2025-06-23 10:04:06,301][com.sinitek.sirm.nocode.controller.ZdLlmController.executeGenerateDiagram:175] - 开始重试图表生成，增强后的需求: 分析考试成绩，语文和数学的平均分

上次执行的SQL：SELECT 
    subject.item ->> 'ZDInput_jvy2' AS subject,
    AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score
FROM 
    zd_page_form_data_64,
    LATERAL jsonb_array_elements(form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item)
WHERE 
    subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学')
GROUP BY 
    subject.item ->> 'ZDInput_jvy2';

上次SQL执行出现错误：SQL查询执行失败: 
### Error querying database.  Cause: java.sql.SQLException: sql injection violation, dbType postgresql, , druid-version 1.2.11, syntax error: TODO pos 202, line 6, column 35, token IDENTIFIER form_data : SELECT 
    subject.item ->> 'ZDInput_jvy2' AS subject,
    AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score
FROM 
    zd_page_form_data_64,
    LATERAL jsonb_array_elements(form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item)
WHERE 
    subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学')
GROUP BY 
    subject.item ->> 'ZDInput_jvy2';
### The error may exist in com/sinitek/sirm/nocode/common/mapper/CommonMapper.xml
### The error may involve com.sinitek.sirm.nocode.common.mapper.CommonMapper.executeDynamicQuery
### The error occurred while executing a query
### SQL: SELECT      subject.item ->> 'ZDInput_jvy2' AS subject,     AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score FROM      zd_page_form_data_64,     LATERAL jsonb_array_elements(form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item) WHERE      subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学') GROUP BY      subject.item ->> 'ZDInput_jvy2';
### Cause: java.sql.SQLException: sql injection violation, dbType postgresql, , druid-version 1.2.11, syntax error: TODO pos 202, line 6, column 35, token IDENTIFIER form_data : SELECT 
    subject.item ->> 'ZDInput_jvy2' AS subject,
    AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score
FROM 
    zd_page_form_data_64,
    LATERAL jsonb_array_elements(form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item)
WHERE 
    subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学')
GROUP BY 
    subject.item ->> 'ZDInput_jvy2';
请根据错误信息调整SQL语句。
[INFO][SIRM][61896,71,http-nio-8097-exec-6][2025-06-23 10:04:06,301][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.formSqlGenerate:343] - 开始生成SQL，表单代码: page_36f15be5f7af4f43a8229d8703e05133, 需求: 分析考试成绩，语文和数学的平均分

上次执行的SQL：SELECT 
    subject.item ->> 'ZDInput_jvy2' AS subject,
    AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score
FROM 
    zd_page_form_data_64,
    LATERAL jsonb_array_elements(form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item)
WHERE 
    subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学')
GROUP BY 
    subject.item ->> 'ZDInput_jvy2';

上次SQL执行出现错误：SQL查询执行失败: 
### Error querying database.  Cause: java.sql.SQLException: sql injection violation, dbType postgresql, , druid-version 1.2.11, syntax error: TODO pos 202, line 6, column 35, token IDENTIFIER form_data : SELECT 
    subject.item ->> 'ZDInput_jvy2' AS subject,
    AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score
FROM 
    zd_page_form_data_64,
    LATERAL jsonb_array_elements(form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item)
WHERE 
    subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学')
GROUP BY 
    subject.item ->> 'ZDInput_jvy2';
### The error may exist in com/sinitek/sirm/nocode/common/mapper/CommonMapper.xml
### The error may involve com.sinitek.sirm.nocode.common.mapper.CommonMapper.executeDynamicQuery
### The error occurred while executing a query
### SQL: SELECT      subject.item ->> 'ZDInput_jvy2' AS subject,     AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score FROM      zd_page_form_data_64,     LATERAL jsonb_array_elements(form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item) WHERE      subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学') GROUP BY      subject.item ->> 'ZDInput_jvy2';
### Cause: java.sql.SQLException: sql injection violation, dbType postgresql, , druid-version 1.2.11, syntax error: TODO pos 202, line 6, column 35, token IDENTIFIER form_data : SELECT 
    subject.item ->> 'ZDInput_jvy2' AS subject,
    AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score
FROM 
    zd_page_form_data_64,
    LATERAL jsonb_array_elements(form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item)
WHERE 
    subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学')
GROUP BY 
    subject.item ->> 'ZDInput_jvy2';
请根据错误信息调整SQL语句。
[INFO][SIRM][61896,71,http-nio-8097-exec-6][2025-06-23 10:05:26,792][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.formSqlGenerate:353] - SQL生成完成，表单代码: page_36f15be5f7af4f43a8229d8703e05133, SQL长度: 376
[INFO][SIRM][61896,71,http-nio-8097-exec-6][2025-06-23 10:05:26,799][com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery:84] - 执行动态SQL查询: SELECT 
    subject.item ->> 'ZDInput_jvy2' AS subject,
    AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score
FROM 
    zd_page_form_data_64 AS t
CROSS JOIN LATERAL 
    jsonb_array_elements(t.form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item)
WHERE 
    subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学')
GROUP BY 
    subject.item ->> 'ZDInput_jvy2'
[ERROR][SIRM][61896,71,http-nio-8097-exec-6][2025-06-23 10:05:26,852][com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery:106] - 执行SQL查询失败: SELECT 
    subject.item ->> 'ZDInput_jvy2' AS subject,
    AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score
FROM 
    zd_page_form_data_64 AS t
CROSS JOIN LATERAL 
    jsonb_array_elements(t.form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item)
WHERE 
    subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学')
GROUP BY 
    subject.item ->> 'ZDInput_jvy2'
org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.sql.SQLException: sql injection violation, dbType postgresql, , druid-version 1.2.11, syntax error: TODO pos 210, line 7, column 27, token IDENTIFIER t : SELECT 
    subject.item ->> 'ZDInput_jvy2' AS subject,
    AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score
FROM 
    zd_page_form_data_64 AS t
CROSS JOIN LATERAL 
    jsonb_array_elements(t.form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item)
WHERE 
    subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学')
GROUP BY 
    subject.item ->> 'ZDInput_jvy2'
### The error may exist in com/sinitek/sirm/nocode/common/mapper/CommonMapper.xml
### The error may involve com.sinitek.sirm.nocode.common.mapper.CommonMapper.executeDynamicQuery
### The error occurred while executing a query
### SQL: SELECT      subject.item ->> 'ZDInput_jvy2' AS subject,     AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score FROM      zd_page_form_data_64 AS t CROSS JOIN LATERAL      jsonb_array_elements(t.form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item) WHERE      subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学') GROUP BY      subject.item ->> 'ZDInput_jvy2'
### Cause: java.sql.SQLException: sql injection violation, dbType postgresql, , druid-version 1.2.11, syntax error: TODO pos 210, line 7, column 27, token IDENTIFIER t : SELECT 
    subject.item ->> 'ZDInput_jvy2' AS subject,
    AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score
FROM 
    zd_page_form_data_64 AS t
CROSS JOIN LATERAL 
    jsonb_array_elements(t.form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item)
WHERE 
    subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学')
GROUP BY 
    subject.item ->> 'ZDInput_jvy2'
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:149) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.6.jar:3.5.6]
	at sun.reflect.GeneratedMethodAccessor189.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at com.sun.proxy.$Proxy140.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:223) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:173) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:78) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.sun.proxy.$Proxy141.executeDynamicQuery(Unknown Source) ~[?:?]
	at com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery(SqlQueryUtil.java:91) ~[classes/:?]
	at com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery(SqlQueryUtil.java:63) ~[classes/:?]
	at com.sinitek.sirm.nocode.controller.ZdLlmController.executeGenerateDiagram(ZdLlmController.java:155) ~[classes/:?]
	at com.sinitek.sirm.nocode.controller.ZdLlmController.executeGenerateDiagram(ZdLlmController.java:176) ~[classes/:?]
	at com.sinitek.sirm.nocode.controller.ZdLlmController.agentGenerateDiagram(ZdLlmController.java:135) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.github.xiaoymin.knife4j.spring.filter.SecurityBasicAuthFilter.doFilter(SecurityBasicAuthFilter.java:87) ~[knife4j-spring-2.0.8.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.11.jar:1.2.11]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.sql.SQLException: sql injection violation, dbType postgresql, , druid-version 1.2.11, syntax error: TODO pos 210, line 7, column 27, token IDENTIFIER t : SELECT 
    subject.item ->> 'ZDInput_jvy2' AS subject,
    AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score
FROM 
    zd_page_form_data_64 AS t
CROSS JOIN LATERAL 
    jsonb_array_elements(t.form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item)
WHERE 
    subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学')
GROUP BY 
    subject.item ->> 'ZDInput_jvy2'
	at com.alibaba.druid.wall.WallFilter.checkInternal(WallFilter.java:828) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.wall.WallFilter.connection_prepareStatement(WallFilter.java:270) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterChainImpl.connection_prepareStatement(FilterChainImpl.java:531) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterAdapter.connection_prepareStatement(FilterAdapter.java:908) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterEventAdapter.connection_prepareStatement(FilterEventAdapter.java:116) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterChainImpl.connection_prepareStatement(FilterChainImpl.java:531) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.proxy.jdbc.ConnectionProxyImpl.prepareStatement(ConnectionProxyImpl.java:326) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.pool.DruidPooledConnection.prepareStatement(DruidPooledConnection.java:362) ~[druid-1.2.11.jar:1.2.11]
	at sun.reflect.GeneratedMethodAccessor75.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.ConnectionLogger.invoke(ConnectionLogger.java:55) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy260.prepareStatement(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:86) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:88) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:59) ~[mybatis-3.5.6.jar:3.5.6]
	at sun.reflect.GeneratedMethodAccessor63.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor.intercept(PaginationInterceptor.java:193) ~[mybatis-plus-extension-3.4.1.jar:3.4.1]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy274.prepare(Unknown Source) ~[?:?]
	at sun.reflect.GeneratedMethodAccessor63.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sinitek.data.mybatis.plugins.CustomerFuncationInterceptor.intercept(CustomerFuncationInterceptor.java:84) ~[sinitek-data-mybatis-7.4.640.jar:7.4.640]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy274.prepare(Unknown Source) ~[?:?]
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.prepareStatement(MybatisSimpleExecutor.java:94) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doQuery(MybatisSimpleExecutor.java:68) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:165) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:92) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at sun.reflect.GeneratedMethodAccessor73.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy273.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.6.jar:3.5.6]
	... 73 more
Caused by: com.alibaba.druid.sql.parser.ParserException: TODO pos 210, line 7, column 27, token IDENTIFIER t
	at com.alibaba.druid.sql.parser.SQLStatementParser.parseStatementList(SQLStatementParser.java:578) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.sql.parser.SQLStatementParser.parseStatementList(SQLStatementParser.java:112) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.wall.WallProvider.checkInternal(WallProvider.java:618) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.wall.WallProvider.check(WallProvider.java:572) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.wall.WallFilter.checkInternal(WallFilter.java:805) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.wall.WallFilter.connection_prepareStatement(WallFilter.java:270) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterChainImpl.connection_prepareStatement(FilterChainImpl.java:531) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterAdapter.connection_prepareStatement(FilterAdapter.java:908) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterEventAdapter.connection_prepareStatement(FilterEventAdapter.java:116) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.filter.FilterChainImpl.connection_prepareStatement(FilterChainImpl.java:531) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.proxy.jdbc.ConnectionProxyImpl.prepareStatement(ConnectionProxyImpl.java:326) ~[druid-1.2.11.jar:1.2.11]
	at com.alibaba.druid.pool.DruidPooledConnection.prepareStatement(DruidPooledConnection.java:362) ~[druid-1.2.11.jar:1.2.11]
	at sun.reflect.GeneratedMethodAccessor75.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.ConnectionLogger.invoke(ConnectionLogger.java:55) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy260.prepareStatement(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:86) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:88) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:59) ~[mybatis-3.5.6.jar:3.5.6]
	at sun.reflect.GeneratedMethodAccessor63.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor.intercept(PaginationInterceptor.java:193) ~[mybatis-plus-extension-3.4.1.jar:3.4.1]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy274.prepare(Unknown Source) ~[?:?]
	at sun.reflect.GeneratedMethodAccessor63.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sinitek.data.mybatis.plugins.CustomerFuncationInterceptor.intercept(CustomerFuncationInterceptor.java:84) ~[sinitek-data-mybatis-7.4.640.jar:7.4.640]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy274.prepare(Unknown Source) ~[?:?]
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.prepareStatement(MybatisSimpleExecutor.java:94) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doQuery(MybatisSimpleExecutor.java:68) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:165) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:92) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at sun.reflect.GeneratedMethodAccessor73.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63) ~[mybatis-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy273.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.6.jar:3.5.6]
	... 73 more
[ERROR][SIRM][61896,71,http-nio-8097-exec-6][2025-06-23 10:05:26,889][com.sinitek.sirm.nocode.controller.ZdLlmController.executeGenerateDiagram:179] - 重试后仍然出现PostgreSQL异常，表单代码: page_36f15be5f7af4f43a8229d8703e05133, 错误: SQL查询执行失败: 
### Error querying database.  Cause: java.sql.SQLException: sql injection violation, dbType postgresql, , druid-version 1.2.11, syntax error: TODO pos 210, line 7, column 27, token IDENTIFIER t : SELECT 
    subject.item ->> 'ZDInput_jvy2' AS subject,
    AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score
FROM 
    zd_page_form_data_64 AS t
CROSS JOIN LATERAL 
    jsonb_array_elements(t.form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item)
WHERE 
    subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学')
GROUP BY 
    subject.item ->> 'ZDInput_jvy2'
### The error may exist in com/sinitek/sirm/nocode/common/mapper/CommonMapper.xml
### The error may involve com.sinitek.sirm.nocode.common.mapper.CommonMapper.executeDynamicQuery
### The error occurred while executing a query
### SQL: SELECT      subject.item ->> 'ZDInput_jvy2' AS subject,     AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score FROM      zd_page_form_data_64 AS t CROSS JOIN LATERAL      jsonb_array_elements(t.form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item) WHERE      subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学') GROUP BY      subject.item ->> 'ZDInput_jvy2'
### Cause: java.sql.SQLException: sql injection violation, dbType postgresql, , druid-version 1.2.11, syntax error: TODO pos 210, line 7, column 27, token IDENTIFIER t : SELECT 
    subject.item ->> 'ZDInput_jvy2' AS subject,
    AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score
FROM 
    zd_page_form_data_64 AS t
CROSS JOIN LATERAL 
    jsonb_array_elements(t.form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item)
WHERE 
    subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学')
GROUP BY 
    subject.item ->> 'ZDInput_jvy2'
com.sinitek.sirm.framework.exception.BussinessException: SQL查询执行失败: 
### Error querying database.  Cause: java.sql.SQLException: sql injection violation, dbType postgresql, , druid-version 1.2.11, syntax error: TODO pos 210, line 7, column 27, token IDENTIFIER t : SELECT 
    subject.item ->> 'ZDInput_jvy2' AS subject,
    AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score
FROM 
    zd_page_form_data_64 AS t
CROSS JOIN LATERAL 
    jsonb_array_elements(t.form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item)
WHERE 
    subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学')
GROUP BY 
    subject.item ->> 'ZDInput_jvy2'
### The error may exist in com/sinitek/sirm/nocode/common/mapper/CommonMapper.xml
### The error may involve com.sinitek.sirm.nocode.common.mapper.CommonMapper.executeDynamicQuery
### The error occurred while executing a query
### SQL: SELECT      subject.item ->> 'ZDInput_jvy2' AS subject,     AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score FROM      zd_page_form_data_64 AS t CROSS JOIN LATERAL      jsonb_array_elements(t.form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item) WHERE      subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学') GROUP BY      subject.item ->> 'ZDInput_jvy2'
### Cause: java.sql.SQLException: sql injection violation, dbType postgresql, , druid-version 1.2.11, syntax error: TODO pos 210, line 7, column 27, token IDENTIFIER t : SELECT 
    subject.item ->> 'ZDInput_jvy2' AS subject,
    AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score
FROM 
    zd_page_form_data_64 AS t
CROSS JOIN LATERAL 
    jsonb_array_elements(t.form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item)
WHERE 
    subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学')
GROUP BY 
    subject.item ->> 'ZDInput_jvy2'
	at com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery(SqlQueryUtil.java:110) ~[classes/:?]
	at com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery(SqlQueryUtil.java:63) ~[classes/:?]
	at com.sinitek.sirm.nocode.controller.ZdLlmController.executeGenerateDiagram(ZdLlmController.java:155) ~[classes/:?]
	at com.sinitek.sirm.nocode.controller.ZdLlmController.executeGenerateDiagram(ZdLlmController.java:176) ~[classes/:?]
	at com.sinitek.sirm.nocode.controller.ZdLlmController.agentGenerateDiagram(ZdLlmController.java:135) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.github.xiaoymin.knife4j.spring.filter.SecurityBasicAuthFilter.doFilter(SecurityBasicAuthFilter.java:87) ~[knife4j-spring-2.0.8.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.11.jar:1.2.11]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
[ERROR][SIRM][61896,71,http-nio-8097-exec-6][2025-06-23 10:05:27,058][com.sinitek.sirm.framework.support.FrontendResponseSupport.logError:240] - 客户端IP: 127.0.0.1, 当前用户orgId: *********, 接口/zhida/frontend/api/nocode/llm/agent-generate-diagram发生异常
java.lang.RuntimeException: SQL执行失败，重试后仍然出现错误: SQL查询执行失败: 
### Error querying database.  Cause: java.sql.SQLException: sql injection violation, dbType postgresql, , druid-version 1.2.11, syntax error: TODO pos 210, line 7, column 27, token IDENTIFIER t : SELECT 
    subject.item ->> 'ZDInput_jvy2' AS subject,
    AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score
FROM 
    zd_page_form_data_64 AS t
CROSS JOIN LATERAL 
    jsonb_array_elements(t.form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item)
WHERE 
    subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学')
GROUP BY 
    subject.item ->> 'ZDInput_jvy2'
### The error may exist in com/sinitek/sirm/nocode/common/mapper/CommonMapper.xml
### The error may involve com.sinitek.sirm.nocode.common.mapper.CommonMapper.executeDynamicQuery
### The error occurred while executing a query
### SQL: SELECT      subject.item ->> 'ZDInput_jvy2' AS subject,     AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score FROM      zd_page_form_data_64 AS t CROSS JOIN LATERAL      jsonb_array_elements(t.form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item) WHERE      subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学') GROUP BY      subject.item ->> 'ZDInput_jvy2'
### Cause: java.sql.SQLException: sql injection violation, dbType postgresql, , druid-version 1.2.11, syntax error: TODO pos 210, line 7, column 27, token IDENTIFIER t : SELECT 
    subject.item ->> 'ZDInput_jvy2' AS subject,
    AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score
FROM 
    zd_page_form_data_64 AS t
CROSS JOIN LATERAL 
    jsonb_array_elements(t.form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item)
WHERE 
    subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学')
GROUP BY 
    subject.item ->> 'ZDInput_jvy2'
	at com.sinitek.sirm.nocode.controller.ZdLlmController.executeGenerateDiagram(ZdLlmController.java:181) ~[classes/:?]
	at com.sinitek.sirm.nocode.controller.ZdLlmController.executeGenerateDiagram(ZdLlmController.java:176) ~[classes/:?]
	at com.sinitek.sirm.nocode.controller.ZdLlmController.agentGenerateDiagram(ZdLlmController.java:135) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.github.xiaoymin.knife4j.spring.filter.SecurityBasicAuthFilter.doFilter(SecurityBasicAuthFilter.java:87) ~[knife4j-spring-2.0.8.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.11.jar:1.2.11]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: com.sinitek.sirm.framework.exception.BussinessException: SQL查询执行失败: 
### Error querying database.  Cause: java.sql.SQLException: sql injection violation, dbType postgresql, , druid-version 1.2.11, syntax error: TODO pos 210, line 7, column 27, token IDENTIFIER t : SELECT 
    subject.item ->> 'ZDInput_jvy2' AS subject,
    AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score
FROM 
    zd_page_form_data_64 AS t
CROSS JOIN LATERAL 
    jsonb_array_elements(t.form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item)
WHERE 
    subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学')
GROUP BY 
    subject.item ->> 'ZDInput_jvy2'
### The error may exist in com/sinitek/sirm/nocode/common/mapper/CommonMapper.xml
### The error may involve com.sinitek.sirm.nocode.common.mapper.CommonMapper.executeDynamicQuery
### The error occurred while executing a query
### SQL: SELECT      subject.item ->> 'ZDInput_jvy2' AS subject,     AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score FROM      zd_page_form_data_64 AS t CROSS JOIN LATERAL      jsonb_array_elements(t.form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item) WHERE      subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学') GROUP BY      subject.item ->> 'ZDInput_jvy2'
### Cause: java.sql.SQLException: sql injection violation, dbType postgresql, , druid-version 1.2.11, syntax error: TODO pos 210, line 7, column 27, token IDENTIFIER t : SELECT 
    subject.item ->> 'ZDInput_jvy2' AS subject,
    AVG(CAST(subject.item ->> 'ZDNumber_wx80' AS NUMERIC)) AS avg_score
FROM 
    zd_page_form_data_64 AS t
CROSS JOIN LATERAL 
    jsonb_array_elements(t.form_data -> 'model' -> 'ZDChildForm_zolh') AS subject(item)
WHERE 
    subject.item ->> 'ZDInput_jvy2' IN ('语文', '数学')
GROUP BY 
    subject.item ->> 'ZDInput_jvy2'
	at com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery(SqlQueryUtil.java:110) ~[classes/:?]
	at com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery(SqlQueryUtil.java:63) ~[classes/:?]
	at com.sinitek.sirm.nocode.controller.ZdLlmController.executeGenerateDiagram(ZdLlmController.java:155) ~[classes/:?]
	... 58 more
[INFO][SIRM][61896,75,http-nio-8097-exec-10][2025-06-23 10:27:26,055][com.sinitek.sirm.nocode.controller.ZdLlmController.agentGenerateDiagram:133] - 开始生成图表，表单代码: page_36f15be5f7af4f43a8229d8703e05133, 需求: 分析考试成绩，语文和数学的平均分
[INFO][SIRM][61896,75,http-nio-8097-exec-10][2025-06-23 10:27:26,064][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.formSqlGenerate:343] - 开始生成SQL，表单代码: page_36f15be5f7af4f43a8229d8703e05133, 需求: 分析考试成绩，语文和数学的平均分
[INFO][SIRM][61896,75,http-nio-8097-exec-10][2025-06-23 10:29:02,174][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.formSqlGenerate:353] - SQL生成完成，表单代码: page_36f15be5f7af4f43a8229d8703e05133, SQL长度: 332
[INFO][SIRM][61896,75,http-nio-8097-exec-10][2025-06-23 10:29:02,178][com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery:84] - 执行动态SQL查询: SELECT
    subject_data->>'ZDInput_jvy2' AS subject,
    AVG((subject_data->>'ZDNumber_wx80')::numeric) AS average_score
FROM
    zd_page_form_data_64,
    jsonb_array_elements(form_data->'model'->'ZDChildForm_zolh') AS subject_data
WHERE
    subject_data->>'ZDInput_jvy2' IN ('语文', '数学')
GROUP BY
    subject_data->>'ZDInput_jvy2';
[INFO][SIRM][61896,75,http-nio-8097-exec-10][2025-06-23 10:29:02,251][com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery:102] - 查询完成，返回2条记录
[INFO][SIRM][61896,75,http-nio-8097-exec-10][2025-06-23 10:29:02,254][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.mermaidGenerate:475] - 开始生成Mermaid图表，用户需求: 需求：分析考试成绩，语文和数学的平均分 

数据：[{"subject":"数学","averageScore":111},{"subject":"语文","averageScore":115}] 

请根据上面的需求和数据生成mermaid代码。
[INFO][SIRM][61896,75,http-nio-8097-exec-10][2025-06-23 10:29:38,866][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.mermaidGenerate:486] - Mermaid图表生成完成，代码长度: 105
[INFO][SIRM][61896,75,http-nio-8097-exec-10][2025-06-23 10:29:38,870][com.sinitek.sirm.nocode.controller.ZdLlmController.executeGenerateDiagram:191] - 图表生成完成，会话ID: null
[INFO][SIRM][61896,74,http-nio-8097-exec-9][2025-06-23 11:08:06,959][com.sinitek.sirm.nocode.controller.ZdLlmController.agentGenerateDiagram:133] - 开始生成图表，表单代码: page_36f15be5f7af4f43a8229d8703e05133, 需求: 分析考试成绩里的语文和数学的平均成绩
[INFO][SIRM][61896,74,http-nio-8097-exec-9][2025-06-23 11:08:06,965][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.formSqlGenerate:343] - 开始生成SQL，表单代码: page_36f15be5f7af4f43a8229d8703e05133, 需求: 分析考试成绩里的语文和数学的平均成绩
[INFO][SIRM][61896,74,http-nio-8097-exec-9][2025-06-23 11:09:39,753][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.formSqlGenerate:353] - SQL生成完成，表单代码: page_36f15be5f7af4f43a8229d8703e05133, SQL长度: 346
[INFO][SIRM][61896,74,http-nio-8097-exec-9][2025-06-23 11:09:39,761][com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery:84] - 执行动态SQL查询: SELECT 
    subject,
    AVG(score) AS avg_score
FROM (
    SELECT 
        (child->>'ZDInput_jvy2') AS subject,
        (child->>'ZDNumber_wx80')::NUMERIC AS score
    FROM 
        zd_page_form_data_64,
        jsonb_array_elements(form_data->'model'->'ZDChildForm_zolh') AS child
) sub
WHERE 
    subject IN ('语文', '数学')
GROUP BY 
    subject;
[INFO][SIRM][61896,74,http-nio-8097-exec-9][2025-06-23 11:09:39,906][com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery:102] - 查询完成，返回2条记录
[INFO][SIRM][61896,74,http-nio-8097-exec-9][2025-06-23 11:09:39,911][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.mermaidGenerate:475] - 开始生成Mermaid图表，用户需求: 需求：分析考试成绩里的语文和数学的平均成绩 

数据：[{"avgScore":111,"subject":"数学"},{"avgScore":115,"subject":"语文"}] 

请根据上面的需求和数据生成mermaid代码。
[INFO][SIRM][61896,74,http-nio-8097-exec-9][2025-06-23 11:10:08,091][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.mermaidGenerate:486] - Mermaid图表生成完成，代码长度: 105
[INFO][SIRM][61896,74,http-nio-8097-exec-9][2025-06-23 11:10:08,091][com.sinitek.sirm.nocode.controller.ZdLlmController.executeGenerateDiagram:191] - 图表生成完成，会话ID: null
[INFO][SIRM][61896,72,http-nio-8097-exec-7][2025-06-23 11:25:41,327][com.sinitek.sirm.nocode.controller.ZdLlmController.agentGenerateDiagram:133] - 开始生成图表，表单代码: page_36f15be5f7af4f43a8229d8703e05133, 需求: 分析考试成绩里的语文和数学平均分
[INFO][SIRM][61896,72,http-nio-8097-exec-7][2025-06-23 11:25:41,332][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.formSqlGenerate:343] - 开始生成SQL，表单代码: page_36f15be5f7af4f43a8229d8703e05133, 需求: 分析考试成绩里的语文和数学平均分
[INFO][SIRM][61896,72,http-nio-8097-exec-7][2025-06-23 11:27:35,001][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.formSqlGenerate:353] - SQL生成完成，表单代码: page_36f15be5f7af4f43a8229d8703e05133, SQL长度: 544
[INFO][SIRM][61896,72,http-nio-8097-exec-7][2025-06-23 11:27:35,005][com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery:84] - 执行动态SQL查询: SELECT
    subject_data->>'ZDInput_jvy2' AS subject,
    AVG((subject_data->>'ZDNumber_wx80')::numeric) AS avg_score
FROM
    zd_page_form_data_64,
    jsonb_array_elements(
        CASE 
            WHEN form_data->'model'->'ZDChildForm_zolh' IS NULL 
            OR form_data->'model'->'ZDChildForm_zolh' = 'null'::jsonb 
            THEN '[]'::jsonb 
            ELSE form_data->'model'->'ZDChildForm_zolh' 
        END
    ) AS subject_data
WHERE
    subject_data->>'ZDInput_jvy2' IN ('语文', '数学')
GROUP BY
    subject_data->>'ZDInput_jvy2';
[INFO][SIRM][61896,72,http-nio-8097-exec-7][2025-06-23 11:27:35,294][com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery:102] - 查询完成，返回2条记录
[INFO][SIRM][61896,72,http-nio-8097-exec-7][2025-06-23 11:27:35,298][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.mermaidGenerate:475] - 开始生成Mermaid图表，用户需求: 需求：分析考试成绩里的语文和数学平均分 

数据：[{"avgScore":111,"subject":"数学"},{"avgScore":115,"subject":"语文"}] 

请根据上面的需求和数据生成mermaid代码。
[INFO][SIRM][61896,72,http-nio-8097-exec-7][2025-06-23 11:28:30,004][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.mermaidGenerate:486] - Mermaid图表生成完成，代码长度: 107
[INFO][SIRM][61896,72,http-nio-8097-exec-7][2025-06-23 11:28:30,006][com.sinitek.sirm.nocode.controller.ZdLlmController.executeGenerateDiagram:191] - 图表生成完成，会话ID: null
