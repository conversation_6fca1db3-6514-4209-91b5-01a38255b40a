package com.sinitek.sirm.nocode.controller;

import cn.hutool.json.JSONUtil;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.nocode.constant.LlmGeneratorConstant;
import com.sinitek.sirm.nocode.dto.LlmAgentCompletionsRequestDTO;
import com.sinitek.sirm.nocode.dto.LlmAgentCompletionsResponseDTO;
import com.sinitek.sirm.nocode.dto.LlmAgentGenFunctionDTO;
import com.sinitek.sirm.nocode.dto.LlmChatCompletionsRequestDTO;
import com.sinitek.sirm.nocode.dto.LlmChatCompletionsResponseDTO;
import com.sinitek.sirm.nocode.dto.LlmChatGenDiagramDTO;
import com.sinitek.sirm.nocode.dto.LlmChatGenDiagramResponseDTO;
import com.sinitek.sirm.nocode.dto.LlmChatSqlDTO;
import com.sinitek.sirm.nocode.service.impl.LlmServiceImpl;
import com.sinitek.sirm.nocode.service.impl.LlmWithImageServiceImpl;
import com.sinitek.sirm.nocode.support.util.SqlQueryUtil;
import com.sinitek.sirm.nocode.util.LlmTextUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 大模型 controller
 *
 * <AUTHOR>
 * @since 2024/4/24
 */
@RestController
@Api(value = "/frontend/api/nocode/llm", tags = "大模型服务")
@RequestMapping("/frontend/api/nocode/llm")
@Slf4j
public class ZdLlmController {

    @Autowired
    private LlmServiceImpl llmService;

    @Autowired
    private LlmWithImageServiceImpl llmWithImageService;

    @Autowired
    private SqlQueryUtil sqlQueryUtil;

    @ApiOperation(value = "测试大模型")
    @RequestMapping(path = "/request-test")
    public RequestResult<LlmChatCompletionsResponseDTO> requestTest(
        @ModelAttribute LlmChatCompletionsRequestDTO requestDTO) {
        return new RequestResult<>(llmService.chatCompletions(requestDTO));
    }

    @ApiOperation(value = "智能体生成脚本")
    @PostMapping("/agent-generate-function")
    public RequestResult<LlmAgentCompletionsResponseDTO> agentGenerateFunction (
        LlmAgentGenFunctionDTO request,
        @RequestParam(value = "images", required = false) List<MultipartFile> images) {
        LlmAgentCompletionsRequestDTO agentCompletionsRequestDTO = new LlmAgentCompletionsRequestDTO();
        agentCompletionsRequestDTO.setPrompt(request.getPrompt());
        Map<String, Object> params = new HashMap<>();
        params.put("schema", request.getSchema());
        if(images != null && !images.isEmpty()){
            try {
                // 将MultipartFile转换为File列表
                List<File> imageFiles = new ArrayList<>();
                for (MultipartFile multipartFile : images) {
                    // 创建临时文件
                    String originalFilename = multipartFile.getOriginalFilename();
                    String suffix = originalFilename != null && originalFilename.contains(".")
                        ? originalFilename.substring(originalFilename.lastIndexOf("."))
                        : ".tmp";
                    File tempFile = File.createTempFile("upload_", suffix);
                    // 将MultipartFile内容复制到临时文件
                    Files.copy(multipartFile.getInputStream(), tempFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                    imageFiles.add(tempFile);
                }
                String imageDesc = llmWithImageService.imageDesc(imageFiles);
                agentCompletionsRequestDTO.setImageDesc( imageDesc );

                // 清理临时文件
                imageFiles.forEach(file -> {
                    try {
                        Files.deleteIfExists(file.toPath());
                    } catch (IOException e) {
                        log.warn("删除临时文件失败: {}", file.getAbsolutePath(), e);
                    }
                });
            } catch (IOException e) {
                log.error("处理上传文件失败", e);
                throw new RuntimeException("处理上传文件失败", e);
            }
        }
        agentCompletionsRequestDTO.setParams(params);
        agentCompletionsRequestDTO.setAgentId( LlmGeneratorConstant.AGENT_GEN_SCHEMA );
        agentCompletionsRequestDTO.setSessionId(request.getSessionId());
        return new RequestResult<>(agentGenerateFunction(agentCompletionsRequestDTO));
    }

    /**
     * 智能生成图表
     * 
     * <p>根据用户需求和表单数据，生成相应的 Mermaid 格式图表。
     * 该接口会先根据表单代码生成 SQL 查询，执行查询获取数据，
     * 然后基于数据和用户需求生成合适的图表类型。</p>
     * 
     * <p>支持的图表类型由 AI 根据数据特征和用户需求自动判断，
     * 包括但不限于：饼图、柱状图、折线图、流程图等。</p>
     *
     * @param request 图表生成请求，包含用户需求描述和表单代码
     * @return 包含 Mermaid 格式图表代码的响应对象
     * @throws RuntimeException 当 SQL 生成失败、数据查询失败或图表生成失败时抛出
     * @since 1.0
     */
    @ApiOperation(value = "智能生成图表", notes = "根据用户需求和表单数据生成 Mermaid 格式图表")
    @PostMapping("/agent-generate-diagram")
    public RequestResult<LlmChatGenDiagramResponseDTO> agentGenerateDiagram(
        LlmChatGenDiagramDTO request) {

        log.info("开始生成图表，表单代码: {}, 需求: {}", request.getFormCode(), request.getPrompt());

        return executeGenerateDiagram(request, false);
    }

    /**
     * 执行图表生成逻辑，支持重试机制
     *
     * @param request 图表生成请求
     * @param isRetry 是否为重试执行
     * @return 图表生成响应
     */
    private RequestResult<LlmChatGenDiagramResponseDTO> executeGenerateDiagram(
        LlmChatGenDiagramDTO request, boolean isRetry) {

        // 1. 构建SQL请求并生成查询语句
        LlmChatSqlDTO sqlRequest = buildSqlRequest(request);
        String generatedSql = llmService.formSqlGenerate(sqlRequest);
        log.debug("生成的SQL语句: {}", generatedSql);
        List<Map<String, Object>> queryResult = null;
        try {
            // 2. 执行SQL查询获取数据
            queryResult = sqlQueryUtil.executeQuery(generatedSql);
            log.debug("查询结果记录数: {}", queryResult.size());
        } catch (Exception sqlE) {
            // 重试一次
            if (!isRetry) {
                log.warn("SQL执行出现P异常，准备重试。错误信息: {}", sqlE.getMessage());

                // 将错误信息补充到原始prompt后面
                String originalPrompt = request.getPrompt();
                String errorMessage = sqlE.getMessage();
                String enhancedPrompt = originalPrompt + "\n\n上次执行的SQL：" + generatedSql +
                    "\n\n上次SQL执行出现错误：" + errorMessage +
                    "\n请根据错误信息调整SQL语句。";

                // 创建新的请求对象进行重试
                LlmChatGenDiagramDTO retryRequest = new LlmChatGenDiagramDTO();
                retryRequest.setPrompt(enhancedPrompt);
                retryRequest.setFormCode(request.getFormCode());
                retryRequest.setSessionId(request.getSessionId());

                log.info("开始重试图表生成，增强后的需求: {}", enhancedPrompt);
                return executeGenerateDiagram(retryRequest, true);
            } else {
                // 已经重试过，直接抛出异常
                log.error("重试后仍然出现PostgreSQL异常，表单代码: {}, 错误: {}",
                    request.getFormCode(), sqlE.getMessage(), sqlE);
                throw new RuntimeException("SQL执行失败，重试后仍然出现错误: " + sqlE.getMessage(), sqlE);
            }
        }

        // 3. 基于数据生成图表
        String diagramCode = generateDiagramFromData(request.getPrompt(), queryResult);

        // 4. 构建响应
        LlmChatGenDiagramResponseDTO response = buildDiagramResponse(request.getSessionId(), diagramCode);

        log.info("图表生成完成，会话ID: {}", request.getSessionId());
        return new RequestResult<>(response);
    }

    /**
     * 构建SQL生成请求
     *
     * @param request 原始图表生成请求
     * @return SQL生成请求对象
     */
    private LlmChatSqlDTO buildSqlRequest(LlmChatGenDiagramDTO request) {
        LlmChatSqlDTO sqlRequest = new LlmChatSqlDTO();
        sqlRequest.setPrompt(request.getPrompt());
        sqlRequest.setFormCode(request.getFormCode());
        sqlRequest.setSessionId(request.getSessionId());
        return sqlRequest;
    }

    /**
     * 基于查询数据生成图表代码
     *
     * @param userPrompt 用户需求描述
     * @param data 查询得到的数据
     * @return Mermaid格式的图表代码
     */
    private String generateDiagramFromData(String userPrompt, List<Map<String, Object>> data) {
        // 预处理数据，将时间戳转换为可读日期格式
        //
        List<Map<String, Object>> processedData = preprocessDataForDiagram(data);

        // 构建图表生成提示词
        String diagramPrompt = String.format(
            "需求：%s \n\n数据：%s \n\n请根据上面的需求和数据生成mermaid代码。",
            userPrompt,
            JSONUtil.toJsonStr(processedData)
        );

        LlmChatCompletionsRequestDTO diagramRequest = new LlmChatCompletionsRequestDTO();
        diagramRequest.setPrompt(diagramPrompt);
        diagramRequest.setFastLlm(true);

        String diagramText = llmService.mermaidGenerate(diagramPrompt);

        // 格式化为标准的Mermaid代码块
        return "```mermaid\n" + LlmTextUtil.formatToMermaid(diagramText) + "\n```";
    }

    /**
     * 构建图表生成响应对象
     *
     * @param sessionId 会话ID
     * @param diagramCode 图表代码
     * @return 图表生成响应对象
     */
    private LlmChatGenDiagramResponseDTO buildDiagramResponse(String sessionId, String diagramCode) {
        LlmChatGenDiagramResponseDTO response = new LlmChatGenDiagramResponseDTO();
        response.setSessionId(sessionId);
        response.setText(diagramCode);
        return response;
    }

    private LlmAgentCompletionsResponseDTO agentGenerateFunction(LlmAgentCompletionsRequestDTO request) {
        try {
            LlmAgentCompletionsResponseDTO responseDTO = llmService.agentCompletions(request);

            // 对响应文本进行二次处理
            String text = processAgentResponseText(responseDTO);
            responseDTO.setText(LlmTextUtil.formatToJS(text));

            return responseDTO;
        } catch (Exception e) {
            log.error("智能代理功能调用失败: {}", e.getMessage(), e);
            throw new RuntimeException("智能代理功能调用失败", e);
        }
    }

    private String processAgentResponseText(LlmAgentCompletionsResponseDTO responseDTO) {
        String text;
        if (responseDTO.getFinishFlag() == LlmGeneratorConstant.FINISH_FLAG_COMPLETE) {
            text = responseDTO.getText();
            if (!text.startsWith("{") || !text.endsWith("}")) {
                text = LlmGeneratorConstant.END_FUNCTION;
            } else {
                Map<String, Object> dataMap = JsonUtil.toMap(text);
                text = (String) dataMap.get(LlmGeneratorConstant.ACTION_INPUT);
            }
        } else {
            text = responseDTO.getText();
        }
        return text;
    }

    /**
     * 预处理数据，将时间戳转换为可读的日期格式
     *
     * @param data 原始数据
     * @return 处理后的数据
     */
    private List<Map<String, Object>> preprocessDataForDiagram(List<Map<String, Object>> data) {
        if (data == null || data.isEmpty()) {
            return data;
        }

        List<Map<String, Object>> processedData = new ArrayList<>();

        for (Map<String, Object> row : data) {
            Map<String, Object> processedRow = new HashMap<>();

            for (Map.Entry<String, Object> entry : row.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();

                // 处理日期时间字段
                Object processedValue = processDateTimeValue(value);
                processedRow.put(key, processedValue);
            }

            processedData.add(processedRow);
        }

        return processedData;
    }

    /**
     * 处理日期时间值
     *
     * @param value 字段值
     * @return 处理后的值
     */
    private Object processDateTimeValue(Object value) {
        if (value == null) {
            return null;
        }
        if(value instanceof Date){
            value = formatDate((Date) value);
        }

        return value;
    }

    /**
     * 格式化Date对象
     *
     * @param date Date对象
     * @return 格式化后的日期字符串
     */
    private String formatDate(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }
}
