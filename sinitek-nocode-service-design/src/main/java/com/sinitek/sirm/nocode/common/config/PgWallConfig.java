package com.sinitek.sirm.nocode.common.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.wall.WallConfig;
import com.alibaba.druid.wall.WallFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2025.0619
 * @description PostgreSQL Druid WallFilter配置，支持PostgreSQL特有的JSON函数和操作符
 * @since 1.0.0-SNAPSHOT
 */
@Component
public class PgWallConfig implements ApplicationRunner {

    private static final Logger log = LoggerFactory.getLogger(PgWallConfig.class);

    @Resource
    private DruidDataSource dataSource;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        dataSource.getProxyFilters().forEach(filter -> {
            if (filter instanceof WallFilter) {
                WallFilter wallFilter = (WallFilter) filter;
                WallConfig config = wallFilter.getConfig();

                // 配置PostgreSQL支持
                configurePostgreSQLSupport(config);

                log.info("PostgreSQL Druid WallFilter配置完成");
            }
        });
    }

    /**
     * 配置PostgreSQL特有功能支持
     *
     * @param config WallConfig配置对象
     */
    private void configurePostgreSQLSupport(WallConfig config) {
        // 移除被禁用的PostgreSQL函数
        removeRestrictedFunctions(config);

        // 配置允许的操作符和语法
        configureAllowedOperators(config);

        // 配置其他PostgreSQL特性
        configurePostgreSQLFeatures(config);
    }

    /**
     * 移除被限制的PostgreSQL函数
     *
     * @param config WallConfig配置对象
     */
    private void removeRestrictedFunctions(WallConfig config) {
        // 移除被禁用的PostgreSQL系统函数
        config.getDenyFunctions().remove("current_schema");
        config.getDenyFunctions().remove("current_database");
        config.getDenyFunctions().remove("version");
        config.getDenyFunctions().remove("pg_version");

        // 移除被禁用的PostgreSQL JSON函数
        config.getDenyFunctions().remove("jsonb_array_elements");
        config.getDenyFunctions().remove("jsonb_array_elements_text");
        config.getDenyFunctions().remove("jsonb_object_keys");
        config.getDenyFunctions().remove("jsonb_each");
        config.getDenyFunctions().remove("jsonb_each_text");
        config.getDenyFunctions().remove("jsonb_path_exists");
        config.getDenyFunctions().remove("jsonb_path_query");
        config.getDenyFunctions().remove("jsonb_path_query_array");
        config.getDenyFunctions().remove("jsonb_extract_path");
        config.getDenyFunctions().remove("jsonb_extract_path_text");
        config.getDenyFunctions().remove("json_array_elements");
        config.getDenyFunctions().remove("json_array_elements_text");
        config.getDenyFunctions().remove("json_object_keys");
        config.getDenyFunctions().remove("json_each");
        config.getDenyFunctions().remove("json_each_text");
        config.getDenyFunctions().remove("json_extract_path");
        config.getDenyFunctions().remove("json_extract_path_text");

        // 移除被禁用的PostgreSQL聚合函数
        config.getDenyFunctions().remove("array_agg");
        config.getDenyFunctions().remove("string_agg");
        config.getDenyFunctions().remove("jsonb_agg");
        config.getDenyFunctions().remove("jsonb_object_agg");

        // 移除被禁用的PostgreSQL类型转换函数
        config.getDenyFunctions().remove("cast");
        config.getDenyFunctions().remove("to_number");
        config.getDenyFunctions().remove("to_char");
        config.getDenyFunctions().remove("to_date");
        config.getDenyFunctions().remove("to_timestamp");

        log.debug("已移除PostgreSQL函数限制");
    }

    /**
     * 配置允许的PostgreSQL操作符
     *
     * @param config WallConfig配置对象
     */
    private void configureAllowedOperators(WallConfig config) {
        // 允许PostgreSQL JSON操作符
        // 注意：Druid的WallConfig主要通过函数名和关键字来限制，
        // 对于操作符（如 ->, ->>），主要是通过SQL解析器来处理
        // 这里我们需要确保不会因为语法解析问题而被拒绝

        // 设置更宽松的解析配置
        config.setStrictSyntaxCheck(false);  // 关闭严格语法检查
        config.setConditionOpAlwayTrueAllow(true);  // 允许条件操作符
        config.setConditionOpBitwseAllow(true);  // 允许位操作符

        log.debug("已配置PostgreSQL操作符支持");
    }

    /**
     * 配置其他PostgreSQL特性
     *
     * @param config WallConfig配置对象
     */
    private void configurePostgreSQLFeatures(WallConfig config) {
        // 允许复杂的子查询和JOIN
        config.setSelectIntoAllow(false);  // 禁止SELECT INTO（安全考虑）
        config.setSelectUnionCheck(false);  // 不检查UNION查询
        config.setSelectWhereAlwayTrueCheck(false);  // 不检查WHERE条件恒真

        // 允许使用别名和复杂表达式
        config.setSelectAllColumnAllow(true);  // 允许SELECT *
        config.setSelectIntoOutfileAllow(false);  // 禁止导出到文件

        // 允许使用LATERAL JOIN（PostgreSQL特有）
        config.setMultiStatementAllow(false);  // 禁止多语句执行（安全考虑）

        // 允许使用WITH子句（CTE）
        config.setWithAllow(true);  // 允许WITH子句

        log.debug("已配置PostgreSQL特性支持");
    }
}
