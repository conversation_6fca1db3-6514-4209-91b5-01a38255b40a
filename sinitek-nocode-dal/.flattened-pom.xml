<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.sinitek.sirm</groupId>
    <artifactId>sinitek-nocode</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>
  <groupId>com.sinitek.sirm</groupId>
  <artifactId>sinitek-nocode-dal</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <properties>
    <maven.compiler.target>8</maven.compiler.target>
    <maven.compiler.source>8</maven.compiler.source>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>
  <dependencies>
    <dependency>
      <groupId>org.postgresql</groupId>
      <artifactId>postgresql</artifactId>
    </dependency>
    <dependency>
      <groupId>com.mysql</groupId>
      <artifactId>mysql-connector-j</artifactId>
    </dependency>
    <dependency>
      <groupId>org.flywaydb</groupId>
      <artifactId>flyway-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.sinitek.sinicube</groupId>
      <artifactId>sinitek-data-mybatis</artifactId>
    </dependency>
    <dependency>
      <groupId>com.sinitek.sirm</groupId>
      <artifactId>sinitek-nocode-api</artifactId>
    </dependency>
  </dependencies>
</project>
