package com.sinitek.sirm.nocode.support.util;

import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.common.mapper.CommonMapper;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import javax.sql.DataSource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * SQL查询工具类
 * 提供安全的动态SQL查询功能，仅支持SELECT查询语句
 *
 * <AUTHOR>
 * @version 2025.0619
 * @since 1.0.0-SNAPSHOT
 */
@Component
public class SqlQueryUtil {

    private static final Logger log = LoggerFactory.getLogger(SqlQueryUtil.class);
    /**
     * 危险SQL关键字正则表达式
     * 匹配可能导致数据修改或删除的SQL关键字
     */
    private static final Pattern DANGEROUS_SQL_PATTERN = Pattern.compile(
            "(?i)\\b(INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|TRUNCATE|EXEC|EXECUTE|DECLARE|MERGE|REPLACE|CALL)\\b"
    );
    /**
     * SELECT语句正则表达式
     * 验证SQL是否为合法的SELECT查询语句
     */
    private static final Pattern SELECT_PATTERN = Pattern.compile(
            "^\\s*SELECT\\b.*", Pattern.CASE_INSENSITIVE | Pattern.DOTALL
    );
    /**
     * 注释清理正则表达式
     * 清理SQL中的单行注释和多行注释
     */
    private static final Pattern COMMENT_PATTERN = Pattern.compile(
            "(--[^\\r\\n]*)|(/\\*[\\s\\S]*?\\*/)", Pattern.MULTILINE
    );
    /**
     * 最大查询结果数量限制
     */
    private static final int MAX_RESULT_SIZE = 10000;
    @Autowired
    private CommonMapper commonMapper;

    /**
     * 执行动态SQL查询
     * 
     * @param sql SQL查询语句，必须是SELECT语句
     * @return 查询结果列表，每行数据以Map形式返回
     * @throws BussinessException 当SQL不安全或执行失败时抛出
     */
    public List<Map<String, Object>> executeQuery(String sql) {
        return executeQuery(sql, null);
    }

    /**
     * 执行动态SQL查询（带参数）
     * 
     * @param sql    SQL查询语句，必须是SELECT语句
     * @param params 查询参数Map，可以为null
     * @return 查询结果列表，每行数据以Map形式返回
     * @throws BussinessException 当SQL不安全或执行失败时抛出
     */
    public List<Map<String, Object>> executeQuery(String sql, Map<String, Object> params) {
        // 参数验证
        if (StringUtils.isBlank(sql)) {
            throw new BussinessException("SQL语句不能为空");
        }

        // SQL安全性验证
        validateSqlSafety(sql);

        try {
            log.info("执行动态SQL查询: {}", sql);
            if (params != null && !params.isEmpty()) {
                log.info("查询参数: {}", params);
            }

            List<Map<String, Object>> result;
            if (params == null || params.isEmpty()) {
                result = commonMapper.executeDynamicQuery(sql);
            } else {
                result = commonMapper.executeDynamicQueryWithParams(sql, params);
            }

            // 结果大小限制
            if (result != null && result.size() > MAX_RESULT_SIZE) {
                log.warn("查询结果数量超过限制: {} > {}", result.size(), MAX_RESULT_SIZE);
                throw new BussinessException("查询结果数量超过限制，最大允许" + MAX_RESULT_SIZE + "条记录");
            }

            log.info("查询完成，返回{}条记录", result == null ? 0 : result.size());
            return result == null ? Collections.emptyList() : result;

        } catch (Exception e) {
            log.error("执行SQL查询失败: {}", sql, e);
            if (e instanceof BussinessException) {
                throw e;
            }
            throw new BussinessException("SQL查询执行失败: " + e.getMessage());
        }
    }

    /**
     * 验证SQL语句的安全性
     * 
     * @param sql 待验证的SQL语句
     * @throws BussinessException 当SQL不安全时抛出
     */
    private void validateSqlSafety(String sql) {
        // 清理注释
        String cleanSql = COMMENT_PATTERN.matcher(sql).replaceAll("");
        
        // 去除多余空白字符
        cleanSql = cleanSql.trim().replaceAll("\\s+", " ");

        // 检查是否为SELECT语句
        if (!SELECT_PATTERN.matcher(cleanSql).matches()) {
            throw new BussinessException("只允许执行SELECT查询语句");
        }

        // 检查危险关键字
        if (DANGEROUS_SQL_PATTERN.matcher(cleanSql).find()) {
            throw new BussinessException("SQL语句包含不允许的操作关键字");
        }

        // 检查分号分隔的多语句
        String[] statements = cleanSql.split(";");
        if (statements.length > 1) {
            // 检查是否有非空的多余语句
            for (int i = 1; i < statements.length; i++) {
                if (StringUtils.isNotBlank(statements[i])) {
                    throw new BussinessException("不允许执行多条SQL语句");
                }
            }
        }

        log.debug("SQL安全性验证通过: {}", cleanSql);
    }

    /**
     * 执行简单的计数查询
     * 
     * @param tableName 表名
     * @param whereClause WHERE条件子句（可选）
     * @return 记录数量
     */
    public Long executeCountQuery(String tableName, String whereClause) {
        if (StringUtils.isBlank(tableName)) {
            throw new BussinessException("表名不能为空");
        }

        StringBuilder sql = new StringBuilder("SELECT COUNT(*) as count FROM ");
        sql.append(tableName);
        
        if (StringUtils.isNotBlank(whereClause)) {
            sql.append(" WHERE ").append(whereClause);
        }

        List<Map<String, Object>> result = executeQuery(sql.toString());
        if (result != null && !result.isEmpty()) {
            Object count = result.get(0).get("count");
            if (count instanceof Number) {
                return ((Number) count).longValue();
            }
        }
        return 0L;
    }

    /**
     * 检查SQL语句是否安全
     * 
     * @param sql SQL语句
     * @return true表示安全，false表示不安全
     */
    public boolean isSqlSafe(String sql) {
        try {
            validateSqlSafety(sql);
            return true;
        } catch (Exception e) {
            log.debug("SQL安全性检查失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取最大查询结果数量限制
     * 
     * @return 最大结果数量
     */
    public int getMaxResultSize() {
        return MAX_RESULT_SIZE;
    }
}
